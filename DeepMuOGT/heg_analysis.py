"""
HEG (Highly Expressed Genes) Analysis Module for DeepMuOGT.

This module implements functionality for identifying and analyzing highly expressed genes
based on KO annotations. It provides tools for:
- Loading HEG KO lists
- Identifying HEG sequences in genomes
- Analyzing HEG vs background gene differences
- Creating HEG-specific feature mappings
"""

import os
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict, Counter

try:
    from .utils.sequence_utils import load_sequences_from_fasta
    from .utils.file_utils import load_ko_annotations, load_heg_ko_list
    from .utils.logging_utils import get_logger
except ImportError:
    from utils.sequence_utils import load_sequences_from_fasta
    from utils.file_utils import load_ko_annotations, load_heg_ko_list
    from utils.logging_utils import get_logger

logger = get_logger(__name__)

class HEGAnalyzer:
    """
    Analyzer for Highly Expressed Genes (HEGs) based on KO annotations.
    
    This class provides functionality to identify HEGs in genomes and analyze
    their characteristics compared to background genes.
    """
    
    def __init__(self, heg_ko_file: Optional[str] = None):
        """
        Initialize the HEG analyzer.
        
        Args:
            heg_ko_file: Path to file containing HEG KO IDs
        """
        self.heg_kos = set()
        
        if heg_ko_file and os.path.exists(heg_ko_file):
            self.heg_kos = load_heg_ko_list(heg_ko_file)
        else:
            logger.warning("HEG KO file not provided or not found. Using default HEG KOs.")
            self._load_default_heg_kos()
    
    def _load_default_heg_kos(self):
        """Load default HEG KO IDs when file is not available."""
        # Common highly expressed genes based on literature
        default_heg_kos = {
            # Ribosomal proteins
            'K02945', 'K02946', 'K02947', 'K02948', 'K02949', 'K02950', 'K02951', 'K02952',
            'K02953', 'K02954', 'K02955', 'K02956', 'K02957', 'K02958', 'K02959', 'K02960',
            'K02961', 'K02962', 'K02963', 'K02964', 'K02965', 'K02966', 'K02967', 'K02968',
            'K02969', 'K02970', 'K02971', 'K02972', 'K02973', 'K02974', 'K02975', 'K02976',
            'K02977', 'K02978', 'K02979', 'K02980', 'K02981', 'K02982', 'K02983', 'K02984',
            'K02985', 'K02986', 'K02987', 'K02988', 'K02989', 'K02990', 'K02991', 'K02992',
            'K02993', 'K02994', 'K02995', 'K02996', 'K02997', 'K02998', 'K02999', 'K03000',
            
            # Translation factors
            'K02358', 'K02359', 'K02360', 'K02361', 'K02362', 'K02363', 'K02364', 'K02365',
            'K02366', 'K02367', 'K02368', 'K02369', 'K02370', 'K02371', 'K02372', 'K02373',
            
            # tRNA synthetases
            'K01867', 'K01868', 'K01869', 'K01870', 'K01871', 'K01872', 'K01873', 'K01874',
            'K01875', 'K01876', 'K01877', 'K01878', 'K01879', 'K01880', 'K01881', 'K01882',
            'K01883', 'K01884', 'K01885', 'K01886', 'K01887', 'K01888', 'K01889', 'K01890',
            
            # RNA polymerase subunits
            'K03040', 'K03041', 'K03042', 'K03043', 'K03044', 'K03045', 'K03046', 'K03047',
            
            # Chaperones and protein folding
            'K03686', 'K04077', 'K04078', 'K04079', 'K04080', 'K04081', 'K04082', 'K04083',
            
            # Central metabolism enzymes
            'K00844', 'K01810', 'K00850', 'K01623', 'K01624', 'K00134', 'K00927',  # Glycolysis
            'K01647', 'K01681', 'K01902', 'K01903', 'K00031', 'K00164', 'K00658',  # TCA cycle
        }
        
        self.heg_kos = default_heg_kos
        logger.info(f"Loaded {len(self.heg_kos)} default HEG KO IDs")
    
    def analyze_genome(self, cds_file: str, ko_file: str) -> Dict[str, any]:
        """
        Analyze HEG content in a genome.
        
        Args:
            cds_file: Path to CDS FASTA file
            ko_file: Path to KO annotation file
        
        Returns:
            Dictionary with HEG analysis results
        """
        try:
            # Load sequences and KO annotations
            sequences = load_sequences_from_fasta(cds_file)
            ko_annotations = load_ko_annotations(ko_file)
            
            if not sequences:
                logger.warning(f"No sequences found in {cds_file}")
                return self._get_empty_analysis()
            
            if not ko_annotations:
                logger.warning(f"No KO annotations found in {ko_file}")
                return self._get_empty_analysis()
            
            # Identify HEG and background sequences
            heg_sequences, bg_sequences = self._classify_sequences(sequences, ko_annotations)
            
            # Calculate analysis metrics
            analysis = {
                'total_sequences': len(sequences),
                'heg_sequences': len(heg_sequences),
                'bg_sequences': len(bg_sequences),
                'ko_mappings': len(ko_annotations),
                'heg_ratio': len(heg_sequences) / len(sequences) if sequences else 0.0,
                'heg_sequence_ids': [seq_id for seq_id, _ in heg_sequences],
                'bg_sequence_ids': [seq_id for seq_id, _ in bg_sequences],
                'heg_ko_distribution': self._analyze_heg_ko_distribution(heg_sequences, ko_annotations),
                'sequence_length_stats': self._analyze_sequence_lengths(heg_sequences, bg_sequences)
            }
            
            logger.info(f"HEG analysis: {analysis['heg_sequences']} HEGs out of {analysis['total_sequences']} total sequences")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing genome HEGs: {e}")
            return self._get_empty_analysis()
    
    def _classify_sequences(self, sequences: List[Tuple[str, str]], 
                          ko_annotations: Dict[str, str]) -> Tuple[List[Tuple[str, str]], List[Tuple[str, str]]]:
        """Classify sequences into HEG and background categories."""
        heg_sequences = []
        bg_sequences = []
        
        for seq_id, sequence in sequences:
            if seq_id in ko_annotations:
                ko_id = ko_annotations[seq_id]
                if ko_id in self.heg_kos:
                    heg_sequences.append((seq_id, sequence))
                else:
                    bg_sequences.append((seq_id, sequence))
            else:
                # Sequences without KO annotation go to background
                bg_sequences.append((seq_id, sequence))
        
        return heg_sequences, bg_sequences
    
    def _analyze_heg_ko_distribution(self, heg_sequences: List[Tuple[str, str]], 
                                   ko_annotations: Dict[str, str]) -> Dict[str, int]:
        """Analyze the distribution of HEG KO IDs."""
        heg_ko_counts = Counter()
        
        for seq_id, _ in heg_sequences:
            if seq_id in ko_annotations:
                ko_id = ko_annotations[seq_id]
                if ko_id in self.heg_kos:
                    heg_ko_counts[ko_id] += 1
        
        return dict(heg_ko_counts)
    
    def _analyze_sequence_lengths(self, heg_sequences: List[Tuple[str, str]], 
                                bg_sequences: List[Tuple[str, str]]) -> Dict[str, float]:
        """Analyze sequence length statistics for HEG vs background."""
        stats = {}
        
        # HEG sequence lengths
        if heg_sequences:
            heg_lengths = [len(seq) for _, seq in heg_sequences]
            stats['heg_mean_length'] = sum(heg_lengths) / len(heg_lengths)
            stats['heg_median_length'] = sorted(heg_lengths)[len(heg_lengths) // 2]
            stats['heg_min_length'] = min(heg_lengths)
            stats['heg_max_length'] = max(heg_lengths)
        else:
            stats['heg_mean_length'] = 0.0
            stats['heg_median_length'] = 0.0
            stats['heg_min_length'] = 0.0
            stats['heg_max_length'] = 0.0
        
        # Background sequence lengths
        if bg_sequences:
            bg_lengths = [len(seq) for _, seq in bg_sequences]
            stats['bg_mean_length'] = sum(bg_lengths) / len(bg_lengths)
            stats['bg_median_length'] = sorted(bg_lengths)[len(bg_lengths) // 2]
            stats['bg_min_length'] = min(bg_lengths)
            stats['bg_max_length'] = max(bg_lengths)
        else:
            stats['bg_mean_length'] = 0.0
            stats['bg_median_length'] = 0.0
            stats['bg_min_length'] = 0.0
            stats['bg_max_length'] = 0.0
        
        # Length ratio
        if stats['bg_mean_length'] > 0:
            stats['heg_bg_length_ratio'] = stats['heg_mean_length'] / stats['bg_mean_length']
        else:
            stats['heg_bg_length_ratio'] = 0.0
        
        return stats
    
    def _get_empty_analysis(self) -> Dict[str, any]:
        """Get empty analysis results."""
        return {
            'total_sequences': 0,
            'heg_sequences': 0,
            'bg_sequences': 0,
            'ko_mappings': 0,
            'heg_ratio': 0.0,
            'heg_sequence_ids': [],
            'bg_sequence_ids': [],
            'heg_ko_distribution': {},
            'sequence_length_stats': {
                'heg_mean_length': 0.0,
                'heg_median_length': 0.0,
                'heg_min_length': 0.0,
                'heg_max_length': 0.0,
                'bg_mean_length': 0.0,
                'bg_median_length': 0.0,
                'bg_min_length': 0.0,
                'bg_max_length': 0.0,
                'heg_bg_length_ratio': 0.0
            }
        }
    
    def create_heg_sequence_map(self, cds_file: str, ko_file: str) -> Dict[str, str]:
        """
        Create a mapping from sequence IDs to HEG status.
        
        Args:
            cds_file: Path to CDS FASTA file
            ko_file: Path to KO annotation file
        
        Returns:
            Dictionary mapping sequence IDs to "HEG" or None
        """
        try:
            sequences = load_sequences_from_fasta(cds_file)
            ko_annotations = load_ko_annotations(ko_file)
            
            heg_map = {}
            
            for seq_id, _ in sequences:
                if seq_id in ko_annotations:
                    ko_id = ko_annotations[seq_id]
                    if ko_id in self.heg_kos:
                        heg_map[seq_id] = "HEG"
            
            logger.info(f"Created HEG map with {len(heg_map)} HEG sequences out of {len(sequences)} total")
            
            return heg_map
            
        except Exception as e:
            logger.error(f"Error creating HEG sequence map: {e}")
            return {}
    
    def get_heg_ko_set(self) -> Set[str]:
        """Get the set of HEG KO IDs."""
        return self.heg_kos.copy()
    
    def is_heg_ko(self, ko_id: str) -> bool:
        """Check if a KO ID is classified as HEG."""
        return ko_id in self.heg_kos
    
    def add_heg_ko(self, ko_id: str) -> None:
        """Add a KO ID to the HEG set."""
        self.heg_kos.add(ko_id)
    
    def remove_heg_ko(self, ko_id: str) -> None:
        """Remove a KO ID from the HEG set."""
        self.heg_kos.discard(ko_id)
