"""
Sequence processing utilities for DeepMuOGT.

This module provides functions for loading, processing, and analyzing biological sequences.
"""

import gzip
import os
from typing import List, Tu<PERSON>, Dict, Optional
from Bio import SeqIO
from Bio.Data import CodonTable
from Bio.Seq import Seq
import numpy as np
import logging

logger = logging.getLogger(__name__)

def load_sequences_from_fasta(fasta_file: str, max_sequences: Optional[int] = None) -> List[Tuple[str, str]]:
    """
    Load sequences from a FASTA file (supports gzipped files).
    
    Args:
        fasta_file: Path to FASTA file (can be gzipped)
        max_sequences: Maximum number of sequences to load (None for all)
    
    Returns:
        List of (sequence_id, sequence) tuples
    """
    sequences = []
    
    if not os.path.exists(fasta_file):
        logger.warning(f"FASTA file not found: {fasta_file}")
        return sequences
    
    try:
        # Determine if file is gzipped
        open_func = gzip.open if fasta_file.endswith('.gz') else open
        mode = 'rt' if fasta_file.endswith('.gz') else 'r'
        
        with open_func(fasta_file, mode) as handle:
            for i, record in enumerate(SeqIO.parse(handle, "fasta")):
                if max_sequences and i >= max_sequences:
                    break
                
                # Clean sequence ID (remove description)
                seq_id = record.id.split()[0]
                sequence = str(record.seq).upper()
                
                # Skip empty sequences
                if len(sequence) > 0:
                    sequences.append((seq_id, sequence))
        
        logger.info(f"Loaded {len(sequences)} sequences from {fasta_file}")
        
    except Exception as e:
        logger.error(f"Error loading sequences from {fasta_file}: {e}")
    
    return sequences

def translate_dna_to_protein(dna_sequence: str, genetic_code: int = 11) -> str:
    """
    Translate DNA sequence to protein using specified genetic code.
    
    Args:
        dna_sequence: DNA sequence string
        genetic_code: Genetic code table ID (default: 11 for bacterial)
    
    Returns:
        Protein sequence string
    """
    try:
        # Remove any non-ATGC characters and ensure uppercase
        clean_seq = ''.join(c for c in dna_sequence.upper() if c in 'ATGC')
        
        # Ensure sequence length is multiple of 3
        if len(clean_seq) % 3 != 0:
            clean_seq = clean_seq[:-(len(clean_seq) % 3)]
        
        if len(clean_seq) == 0:
            return ""
        
        # Translate using BioPython
        seq_obj = Seq(clean_seq)
        protein = str(seq_obj.translate(table=genetic_code, stop_symbol='*'))
        
        # Remove stop codons from the end
        protein = protein.rstrip('*')
        
        return protein
        
    except Exception as e:
        logger.warning(f"Error translating DNA sequence: {e}")
        return ""

def get_codon_table(genetic_code: int = 11) -> Dict[str, str]:
    """
    Get codon to amino acid mapping for specified genetic code.
    
    Args:
        genetic_code: Genetic code table ID (default: 11 for bacterial)
    
    Returns:
        Dictionary mapping codons to amino acids
    """
    try:
        table = CodonTable.unambiguous_dna_by_id[genetic_code]
        return table.forward_table
    except Exception as e:
        logger.error(f"Error getting codon table for genetic code {genetic_code}: {e}")
        # Return standard genetic code as fallback
        return CodonTable.unambiguous_dna_by_id[1].forward_table

def one_hot_encode_sequence(sequence: str, alphabet: str = "ATGC") -> np.ndarray:
    """
    One-hot encode a biological sequence.
    
    Args:
        sequence: Biological sequence string
        alphabet: Alphabet to use for encoding (default: "ATGC" for DNA)
    
    Returns:
        One-hot encoded array of shape (len(sequence), len(alphabet))
    """
    # Create mapping from characters to indices
    char_to_idx = {char: i for i, char in enumerate(alphabet)}
    
    # Initialize one-hot array
    encoded = np.zeros((len(sequence), len(alphabet)), dtype=np.float32)
    
    # Fill in one-hot encoding
    for i, char in enumerate(sequence.upper()):
        if char in char_to_idx:
            encoded[i, char_to_idx[char]] = 1.0
    
    return encoded

def calculate_gc_content(sequence: str) -> float:
    """
    Calculate GC content of a sequence.
    
    Args:
        sequence: DNA sequence string
    
    Returns:
        GC content as a fraction (0-1)
    """
    if not sequence:
        return 0.0
    
    sequence = sequence.upper()
    gc_count = sequence.count('G') + sequence.count('C')
    total_count = len([c for c in sequence if c in 'ATGC'])
    
    return gc_count / total_count if total_count > 0 else 0.0

def extract_codons(dna_sequence: str, frame: int = 0) -> List[str]:
    """
    Extract codons from DNA sequence.
    
    Args:
        dna_sequence: DNA sequence string
        frame: Reading frame (0, 1, or 2)
    
    Returns:
        List of codon strings
    """
    sequence = dna_sequence.upper()[frame:]
    codons = []
    
    for i in range(0, len(sequence) - 2, 3):
        codon = sequence[i:i+3]
        if len(codon) == 3 and all(c in 'ATGC' for c in codon):
            codons.append(codon)
    
    return codons

def reverse_complement(sequence: str) -> str:
    """
    Get reverse complement of DNA sequence.
    
    Args:
        sequence: DNA sequence string
    
    Returns:
        Reverse complement sequence
    """
    complement = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G'}
    return ''.join(complement.get(c, c) for c in sequence.upper()[::-1])
