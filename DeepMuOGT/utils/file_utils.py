"""
File handling utilities for DeepMuOGT.

This module provides functions for finding genome files, loading annotations,
and saving feature data.
"""

import os
import gzip
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

def find_genome_files(genome_id: str, base_dirs: Dict[str, str]) -> Dict[str, Optional[str]]:
    """
    Find genome-related files for a given genome ID.
    
    Args:
        genome_id: Genome identifier (e.g., "GCF_000008365.1")
        base_dirs: Dictionary with keys like 'genome_dir', 'cds_dir', 'protein_dir', etc.
    
    Returns:
        Dictionary mapping file types to file paths (None if not found)
    """
    files = {
        'genome_file': None,
        'cds_file': None,
        'protein_file': None,
        'ko_file': None,
        'trna_file': None,
        'rrna_file': None
    }
    
    # Determine kingdom from genome_id prefix
    if genome_id.startswith('GCF_') or genome_id.startswith('GCA_'):
        # Look in kingdom-specific directories
        kingdoms = ['Bacteria', 'Archaea', 'Fungi', 'Algae']
        
        for kingdom in kingdoms:
            # Check for genome file
            if 'genome_dir' in base_dirs:
                genome_path = os.path.join(base_dirs['genome_dir'], kingdom, genome_id)
                if os.path.exists(genome_path):
                    # Look for genomic.fna.gz file
                    genomic_file = os.path.join(genome_path, f"{genome_id}_genomic.fna.gz")
                    if os.path.exists(genomic_file):
                        files['genome_file'] = genomic_file
                        break
        
        # Check for CDS file
        if 'cds_dir' in base_dirs:
            cds_file = os.path.join(base_dirs['cds_dir'], f"{genome_id}_CDS.fasta.gz")
            if os.path.exists(cds_file):
                files['cds_file'] = cds_file
        
        # Check for protein file
        if 'protein_dir' in base_dirs:
            for kingdom in kingdoms:
                protein_file = os.path.join(base_dirs['protein_dir'], kingdom, f"{genome_id}_protein.faa.gz")
                if os.path.exists(protein_file):
                    files['protein_file'] = protein_file
                    break
        
        # Check for tRNA file
        if 'trna_dir' in base_dirs:
            trna_file = os.path.join(base_dirs['trna_dir'], f"{genome_id}_tRNA.fasta.gz")
            if os.path.exists(trna_file):
                files['trna_file'] = trna_file
        
        # Check for rRNA file
        if 'rrna_dir' in base_dirs:
            rrna_file = os.path.join(base_dirs['rrna_dir'], f"{genome_id}_rRNA.fasta.gz")
            if os.path.exists(rrna_file):
                files['rrna_file'] = rrna_file
    
    return files

def load_ko_annotations(ko_file: str) -> Dict[str, str]:
    """
    Load KO annotations from a file.
    
    Args:
        ko_file: Path to KO annotation file
    
    Returns:
        Dictionary mapping gene IDs to KO IDs
    """
    ko_map = {}
    
    if not os.path.exists(ko_file):
        logger.warning(f"KO file not found: {ko_file}")
        return ko_map
    
    try:
        with open(ko_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        gene_id = parts[0]
                        ko_id = parts[1]
                        ko_map[gene_id] = ko_id
        
        logger.info(f"Loaded {len(ko_map)} KO annotations from {ko_file}")
        
    except Exception as e:
        logger.error(f"Error loading KO annotations from {ko_file}: {e}")
    
    return ko_map

def load_heg_ko_list(heg_file: str) -> set:
    """
    Load list of HEG (Highly Expressed Genes) KO IDs.
    
    Args:
        heg_file: Path to HEG KO list file
    
    Returns:
        Set of HEG KO IDs
    """
    heg_kos = set()
    
    if not os.path.exists(heg_file):
        logger.warning(f"HEG file not found: {heg_file}")
        return heg_kos
    
    try:
        with open(heg_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    heg_kos.add(line)
        
        logger.info(f"Loaded {len(heg_kos)} HEG KO IDs from {heg_file}")
        
    except Exception as e:
        logger.error(f"Error loading HEG KO list from {heg_file}: {e}")
    
    return heg_kos

def save_features_to_file(features: Dict[str, Any], output_file: str) -> None:
    """
    Save features to a file (supports .npz and .json formats).
    
    Args:
        features: Dictionary of features
        output_file: Output file path
    """
    try:
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        if output_file.endswith('.npz'):
            # Convert all values to numpy arrays for npz format
            np_features = {}
            for key, value in features.items():
                if isinstance(value, (int, float)):
                    np_features[key] = np.array([value])
                elif isinstance(value, (list, tuple)):
                    np_features[key] = np.array(value)
                else:
                    np_features[key] = np.array([str(value)])
            
            np.savez_compressed(output_file, **np_features)
            
        elif output_file.endswith('.json'):
            # Save as JSON
            with open(output_file, 'w') as f:
                json.dump(features, f, indent=2, default=str)
        
        else:
            logger.warning(f"Unsupported file format for {output_file}")
            return
        
        logger.info(f"Saved {len(features)} features to {output_file}")
        
    except Exception as e:
        logger.error(f"Error saving features to {output_file}: {e}")

def load_metadata_file(metadata_file: str) -> pd.DataFrame:
    """
    Load metadata file containing genome information.
    
    Args:
        metadata_file: Path to metadata CSV file
    
    Returns:
        DataFrame with genome metadata
    """
    try:
        df = pd.read_csv(metadata_file)
        logger.info(f"Loaded metadata for {len(df)} genomes from {metadata_file}")
        return df
    except Exception as e:
        logger.error(f"Error loading metadata from {metadata_file}: {e}")
        return pd.DataFrame()

def combine_feature_files(feature_dir: str, output_file: str) -> None:
    """
    Combine individual feature files into a single TSV file.
    
    Args:
        feature_dir: Directory containing individual feature files
        output_file: Output TSV file path
    """
    try:
        all_features = []
        
        # Find all .npz files in the feature directory
        for filename in os.listdir(feature_dir):
            if filename.endswith('_features.npz'):
                genome_id = filename.replace('_features.npz', '')
                filepath = os.path.join(feature_dir, filename)
                
                # Load features
                with np.load(filepath) as data:
                    features = {'genome_id': genome_id}
                    for key in data.files:
                        value = data[key]
                        if value.ndim == 0:
                            features[key] = float(value)
                        elif len(value) == 1:
                            features[key] = float(value[0])
                        else:
                            # For arrays, take the first element or convert to string
                            features[key] = str(value)
                    
                    all_features.append(features)
        
        if all_features:
            # Create DataFrame and save as TSV
            df = pd.DataFrame(all_features)
            df.to_csv(output_file, sep='\t', index=False)
            logger.info(f"Combined {len(all_features)} feature files into {output_file}")
        else:
            logger.warning(f"No feature files found in {feature_dir}")
            
    except Exception as e:
        logger.error(f"Error combining feature files: {e}")

def get_genome_kingdom(genome_id: str, base_dirs: Dict[str, str]) -> Optional[str]:
    """
    Determine the kingdom of a genome based on file location.
    
    Args:
        genome_id: Genome identifier
        base_dirs: Dictionary with directory paths
    
    Returns:
        Kingdom name or None if not found
    """
    if 'genome_dir' not in base_dirs:
        return None
    
    kingdoms = ['Bacteria', 'Archaea', 'Fungi', 'Algae']
    
    for kingdom in kingdoms:
        genome_path = os.path.join(base_dirs['genome_dir'], kingdom, genome_id)
        if os.path.exists(genome_path):
            return kingdom
    
    return None
