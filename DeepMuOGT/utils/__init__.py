"""
Utility modules for DeepMuOGT feature extraction.

This package contains utility functions and classes used across the feature extraction modules.
"""

from .sequence_utils import (
    load_sequences_from_fasta,
    translate_dna_to_protein,
    get_codon_table,
    one_hot_encode_sequence
)

from .logging_utils import get_logger, setup_logging

from .file_utils import (
    find_genome_files,
    load_ko_annotations,
    save_features_to_file
)

__all__ = [
    'load_sequences_from_fasta',
    'translate_dna_to_protein',
    'get_codon_table',
    'one_hot_encode_sequence',
    'get_logger',
    'setup_logging',
    'find_genome_files',
    'load_ko_annotations',
    'save_features_to_file'
]
