"""
Main Feature Extraction Module for DeepMuOGT.

This module provides the main interface for extracting comprehensive features
from genomic data for optimal growth temperature prediction. It integrates
all feature calculators and provides batch processing capabilities.
"""

import os
import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Union, Any
import multiprocessing as mp
from tqdm import tqdm

try:
    from .codon_features import CodonFeatureCalculator
    from .amino_acid_features import AminoAcidFeatureCalculator
    from .genomic_features import GenomicFeatureCalculator
    from .taxonomy_features import TaxonomyFeatureCalculator
    from .pathway_features import PathwayFeatureCalculator
    from .heg_analysis import HEGAnalyzer

    from .utils.sequence_utils import load_sequences_from_fasta
    from .utils.file_utils import (
        find_genome_files, load_ko_annotations, save_features_to_file,
        load_metadata_file, combine_feature_files, get_genome_kingdom
    )
    from .utils.logging_utils import get_logger, setup_logging
except ImportError:
    # Fallback for direct execution
    from codon_features import CodonFeatureCalculator
    from amino_acid_features import AminoAcidFeatureCalculator
    from genomic_features import GenomicFeatureCalculator
    from taxonomy_features import TaxonomyFeatureCalculator
    from pathway_features import PathwayFeatureCalculator
    from heg_analysis import HEGAnalyzer

    from utils.sequence_utils import load_sequences_from_fasta
    from utils.file_utils import (
        find_genome_files, load_ko_annotations, save_features_to_file,
        load_metadata_file, combine_feature_files, get_genome_kingdom
    )
    from utils.logging_utils import get_logger, setup_logging

logger = get_logger(__name__)

def extract_features_for_genome(
    genome_id: str,
    base_dirs: Dict[str, str],
    taxid: Optional[Union[str, int]] = None,
    kegg_map_file: Optional[str] = None,
    heg_ko_file: Optional[str] = None,
    advanced_codon_features: bool = True,
    genetic_code: int = 11
) -> Dict[str, float]:
    """
    Extract all features for a single genome.
    
    Args:
        genome_id: Genome identifier (e.g., "GCF_000008365.1")
        base_dirs: Dictionary with directory paths (genome_dir, cds_dir, protein_dir, etc.)
        taxid: NCBI taxonomy ID
        kegg_map_file: Path to KEGG mapping file
        heg_ko_file: Path to HEG KO list file
        advanced_codon_features: Whether to calculate advanced codon features
        genetic_code: Genetic code to use for translation
    
    Returns:
        Dictionary of extracted features
    """
    logger.info(f"Extracting features for genome {genome_id}")
    
    features = {}
    
    try:
        # Find genome files
        genome_files = find_genome_files(genome_id, base_dirs)
        
        # Initialize feature calculators
        codon_calc = CodonFeatureCalculator(genetic_code=genetic_code, use_heg_features=True)
        aa_calc = AminoAcidFeatureCalculator(genetic_code=genetic_code, use_heg_features=True)
        genomic_calc = GenomicFeatureCalculator()
        taxonomy_calc = TaxonomyFeatureCalculator()
        pathway_calc = PathwayFeatureCalculator(kegg_map_file)
        heg_analyzer = HEGAnalyzer(heg_ko_file)
        
        # Extract genomic features
        if genome_files['genome_file']:
            logger.info(f"Extracting genomic features from {genome_files['genome_file']}")
            genomic_features = genomic_calc.calculate_features(genome_files['genome_file'])
            features.update(genomic_features)
            logger.info(f"Added {len(genomic_features)} genomic features")
        
        # Extract taxonomy features
        if taxid:
            logger.info(f"Extracting taxonomy features for taxid {taxid}")
            taxonomy_features = taxonomy_calc.calculate_features(taxid)
            features.update(taxonomy_features)
            logger.info(f"Added {len(taxonomy_features)} taxonomy features")
        
        # Load sequences and KO annotations for sequence-based features
        cds_sequences = []
        protein_sequences = []
        ko_annotations = {}
        heg_sequence_ids = set()
        
        if genome_files['cds_file']:
            cds_sequences = load_sequences_from_fasta(genome_files['cds_file'])
            logger.info(f"Loaded {len(cds_sequences)} CDS sequences")
        
        if genome_files['protein_file']:
            protein_sequences = load_sequences_from_fasta(genome_files['protein_file'])
            logger.info(f"Loaded {len(protein_sequences)} protein sequences")
        
        if genome_files['ko_file']:
            ko_annotations = load_ko_annotations(genome_files['ko_file'])
            logger.info(f"Loaded {len(ko_annotations)} KO annotations")
            
            # Analyze HEGs
            heg_analysis = heg_analyzer.analyze_genome(genome_files['cds_file'], genome_files['ko_file'])
            heg_sequence_ids = set(heg_analysis['heg_sequence_ids'])
            
            # Add HEG analysis features
            heg_features = {
                'heg_total_sequences': heg_analysis['total_sequences'],
                'heg_count': heg_analysis['heg_sequences'],
                'bg_count': heg_analysis['bg_sequences'],
                'heg_ratio': heg_analysis['heg_ratio']
            }
            features.update(heg_features)
            logger.info(f"Added {len(heg_features)} HEG analysis features")
        
        # Extract codon features
        if cds_sequences and advanced_codon_features:
            logger.info("Extracting codon features")
            codon_features = codon_calc.calculate_features(cds_sequences, heg_sequence_ids)
            features.update(codon_features)
            logger.info(f"Added {len(codon_features)} codon features")
        
        # Extract amino acid features
        if protein_sequences:
            logger.info("Extracting amino acid features")
            aa_features = aa_calc.calculate_features(protein_sequences, heg_sequence_ids, from_dna=False)
            features.update(aa_features)
            logger.info(f"Added {len(aa_features)} amino acid features")
        elif cds_sequences:
            # Translate CDS to protein if protein sequences not available
            logger.info("Extracting amino acid features from translated CDS")
            aa_features = aa_calc.calculate_features(cds_sequences, heg_sequence_ids, from_dna=True)
            features.update(aa_features)
            logger.info(f"Added {len(aa_features)} amino acid features")
        
        # Extract pathway features
        if genome_files['ko_file']:
            logger.info("Extracting pathway features")
            pathway_features = pathway_calc.calculate_features(genome_files['ko_file'])
            features.update(pathway_features)
            logger.info(f"Added {len(pathway_features)} pathway features")
        
        logger.info(f"Successfully extracted {len(features)} total features for genome {genome_id}")
        
        return features
        
    except Exception as e:
        logger.error(f"Error extracting features for genome {genome_id}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {}

def extract_features_batch(
    metadata_file: str,
    output_dir: str = "features",
    base_dirs: Optional[Dict[str, str]] = None,
    kegg_map_file: Optional[str] = None,
    heg_ko_file: Optional[str] = None,
    advanced_codon_features: bool = True,
    num_processes: int = mp.cpu_count(),
    genetic_code: int = 11
) -> None:
    """
    Extract features for all genomes in a metadata file.
    
    Args:
        metadata_file: Path to metadata CSV file
        output_dir: Output directory for feature files
        base_dirs: Dictionary with directory paths
        kegg_map_file: Path to KEGG mapping file
        heg_ko_file: Path to HEG KO list file
        advanced_codon_features: Whether to calculate advanced codon features
        num_processes: Number of processes for parallel processing
        genetic_code: Genetic code to use for translation
    """
    logger.info(f"Starting batch feature extraction from {metadata_file}")
    
    # Load metadata
    metadata_df = load_metadata_file(metadata_file)
    if metadata_df.empty:
        logger.error("No metadata loaded. Exiting.")
        return
    
    # Set default base directories if not provided
    if base_dirs is None:
        base_dirs = {
            'genome_dir': '.',
            'cds_dir': 'CDS',
            'protein_dir': 'protein',
            'trna_dir': 'tRNA',
            'rrna_dir': 'rRNA'
        }
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each genome
    successful_extractions = 0
    failed_extractions = 0
    
    for idx, row in tqdm(metadata_df.iterrows(), total=len(metadata_df), desc="Extracting features"):
        genome_id = row['genome_id']
        taxid = row.get('taxid', None)
        
        try:
            # Extract features
            features = extract_features_for_genome(
                genome_id=genome_id,
                base_dirs=base_dirs,
                taxid=taxid,
                kegg_map_file=kegg_map_file,
                heg_ko_file=heg_ko_file,
                advanced_codon_features=advanced_codon_features,
                genetic_code=genetic_code
            )
            
            if features:
                # Save features
                output_file = os.path.join(output_dir, f"{genome_id}_features.npz")
                save_features_to_file(features, output_file)
                successful_extractions += 1
            else:
                logger.warning(f"No features extracted for genome {genome_id}")
                failed_extractions += 1
                
        except Exception as e:
            logger.error(f"Error processing genome {genome_id}: {e}")
            failed_extractions += 1
    
    logger.info(f"Batch extraction completed: {successful_extractions} successful, {failed_extractions} failed")

def combine_feature_files(feature_dir: str, output_file: str) -> None:
    """
    Combine individual feature files into a single TSV file.
    
    Args:
        feature_dir: Directory containing individual feature files
        output_file: Output TSV file path
    """
    logger.info(f"Combining feature files from {feature_dir} into {output_file}")
    combine_feature_files(feature_dir, output_file)
    logger.info(f"Feature combination completed: {output_file}")

def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Extract features for optimal growth temperature prediction")
    parser.add_argument("metadata", help="Path to metadata CSV file")
    parser.add_argument("--output-dir", default="features", help="Output directory for feature files")
    parser.add_argument("--genome-dir", help="Directory containing genome files")
    parser.add_argument("--cds-dir", help="Directory containing CDS files")
    parser.add_argument("--protein-dir", help="Directory containing protein files")
    parser.add_argument("--kegg-map", help="Path to KEGG mapping file")
    parser.add_argument("--heg-ko-list", help="Path to HEG KO list file")
    parser.add_argument("--no-advanced-codon", action="store_true", help="Skip advanced codon features")
    parser.add_argument("--num-processes", type=int, default=mp.cpu_count(), help="Number of processes")
    parser.add_argument("--combine", action="store_true", help="Combine feature files into TSV")
    parser.add_argument("--output-tsv", help="Output TSV file path")
    parser.add_argument("--log-level", default="INFO", help="Logging level")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    # Prepare base directories
    base_dirs = {}
    if args.genome_dir:
        base_dirs['genome_dir'] = args.genome_dir
    if args.cds_dir:
        base_dirs['cds_dir'] = args.cds_dir
    if args.protein_dir:
        base_dirs['protein_dir'] = args.protein_dir
    
    # Extract features
    extract_features_batch(
        args.metadata,
        args.output_dir,
        base_dirs,
        args.kegg_map,
        args.heg_ko_list,
        not args.no_advanced_codon,
        args.num_processes
    )
    
    # Combine feature files if requested
    if args.combine:
        if not args.output_tsv:
            args.output_tsv = os.path.join(args.output_dir, "combined_features.tsv")
        combine_feature_files(args.output_dir, args.output_tsv)

if __name__ == "__main__":
    main()
