"""
Codon Feature Calculator for DeepMuOGT.

This module implements comprehensive codon usage analysis including:
- Codon Usage Bias (CUB)
- Codon Pair Bias (CPB)
- Codon Consistency
- Remote Interaction Analysis
- CAI (Codon Adaptation Index)
- ENC (Effective Number of Codons)
- HEG vs Background analysis
"""

import numpy as np
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Set, Optional
from scipy.spatial.distance import jensenshannon
import math
import itertools

try:
    from .utils.sequence_utils import extract_codons, get_codon_table, translate_dna_to_protein
    from .utils.logging_utils import get_logger
except ImportError:
    from utils.sequence_utils import extract_codons, get_codon_table, translate_dna_to_protein
    from utils.logging_utils import get_logger

logger = get_logger(__name__)

class CodonFeatureCalculator:
    """
    Calculates comprehensive codon usage features for microbial growth prediction.
    
    This class implements various codon usage metrics including traditional measures
    like CAI and ENC, as well as advanced metrics like codon pair bias and remote
    interaction analysis.
    """
    
    def __init__(self, genetic_code: int = 11, use_heg_features: bool = True):
        """
        Initialize the codon feature calculator.
        
        Args:
            genetic_code: Genetic code table ID (default: 11 for bacterial)
            use_heg_features: Whether to calculate HEG-specific features
        """
        self.genetic_code = genetic_code
        self.use_heg_features = use_heg_features
        self.codon_table = get_codon_table(genetic_code)
        
        # Standard genetic code codons
        self.all_codons = [
            'TTT', 'TTC', 'TTA', 'TTG', 'TCT', 'TCC', 'TCA', 'TCG',
            'TAT', 'TAC', 'TAA', 'TAG', 'TGT', 'TGC', 'TGA', 'TGG',
            'CTT', 'CTC', 'CTA', 'CTG', 'CCT', 'CCC', 'CCA', 'CCG',
            'CAT', 'CAC', 'CAA', 'CAG', 'CGT', 'CGC', 'CGA', 'CGG',
            'ATT', 'ATC', 'ATA', 'ATG', 'ACT', 'ACC', 'ACA', 'ACG',
            'AAT', 'AAC', 'AAA', 'AAG', 'AGT', 'AGC', 'AGA', 'AGG',
            'GTT', 'GTC', 'GTA', 'GTG', 'GCT', 'GCC', 'GCA', 'GCG',
            'GAT', 'GAC', 'GAA', 'GAG', 'GGT', 'GGC', 'GGA', 'GGG'
        ]
        
        # Group codons by amino acid
        self.aa_to_codons = defaultdict(list)
        for codon, aa in self.codon_table.items():
            self.aa_to_codons[aa].append(codon)
        
        # Stop codons
        self.stop_codons = {'TAA', 'TAG', 'TGA'}
    
    def calculate_features(self, sequences: List[Tuple[str, str]], 
                         heg_sequences: Optional[Set[str]] = None) -> Dict[str, float]:
        """
        Calculate all codon features from DNA sequences.
        
        Args:
            sequences: List of (sequence_id, dna_sequence) tuples
            heg_sequences: Set of sequence IDs that are highly expressed genes
        
        Returns:
            Dictionary of codon features
        """
        if not sequences:
            logger.warning("No sequences provided for codon analysis")
            return {}
        
        features = {}
        
        # Extract all codons from sequences
        all_codons = []
        heg_codons = []
        bg_codons = []
        
        for seq_id, dna_seq in sequences:
            codons = extract_codons(dna_seq)
            all_codons.extend(codons)
            
            if self.use_heg_features and heg_sequences:
                if seq_id in heg_sequences:
                    heg_codons.extend(codons)
                else:
                    bg_codons.extend(codons)
        
        if not all_codons:
            logger.warning("No valid codons found in sequences")
            return {}
        
        # Calculate basic codon features
        features.update(self._calculate_basic_features(all_codons))
        
        # Calculate advanced features
        features.update(self._calculate_advanced_features(sequences))
        
        # Calculate HEG-specific features if requested
        if self.use_heg_features and heg_sequences and heg_codons and bg_codons:
            features.update(self._calculate_heg_features(heg_codons, bg_codons))
        
        return features
    
    def _calculate_basic_features(self, codons: List[str]) -> Dict[str, float]:
        """Calculate basic codon usage features."""
        features = {}
        
        # Count codons
        codon_counts = Counter(codons)
        total_codons = len(codons)
        
        if total_codons == 0:
            return features
        
        # Calculate codon frequencies
        codon_freqs = {codon: count / total_codons for codon, count in codon_counts.items()}
        
        # Calculate CUB (Codon Usage Bias)
        features['CUB'] = self._calculate_cub(codon_freqs)
        
        # Calculate CAI (Codon Adaptation Index)
        features['CAI'] = self._calculate_cai(codons)
        
        # Calculate ENC (Effective Number of Codons)
        features['ENC'] = self._calculate_enc(codon_counts)
        
        # Calculate GC content at different positions
        gc_contents = self._calculate_gc_contents(codons)
        features.update(gc_contents)
        
        # Calculate overall GC content
        all_bases = ''.join(codons)
        if all_bases:
            gc_count = all_bases.count('G') + all_bases.count('C')
            features['GC_content'] = gc_count / len(all_bases)
        else:
            features['GC_content'] = 0.0
        
        return features
    
    def _calculate_cub(self, codon_freqs: Dict[str, float]) -> float:
        """Calculate Codon Usage Bias."""
        if not codon_freqs:
            return 0.0
        
        # Calculate expected uniform frequency for each amino acid
        bias_sum = 0.0
        aa_count = 0
        
        for aa, codons in self.aa_to_codons.items():
            if len(codons) > 1:  # Only consider amino acids with multiple codons
                # Get frequencies for this amino acid's codons
                aa_freqs = [codon_freqs.get(codon, 0.0) for codon in codons]
                aa_total = sum(aa_freqs)
                
                if aa_total > 0:
                    # Calculate bias as deviation from uniform distribution
                    uniform_freq = aa_total / len(codons)
                    bias = sum(abs(freq - uniform_freq) for freq in aa_freqs)
                    bias_sum += bias / (2 * aa_total)  # Normalize
                    aa_count += 1
        
        return bias_sum / aa_count if aa_count > 0 else 0.0

    def _calculate_cai(self, codons: List[str]) -> float:
        """Calculate Codon Adaptation Index."""
        if not codons:
            return 0.0

        # Use the most frequent codon for each amino acid as reference
        aa_codon_counts = defaultdict(lambda: defaultdict(int))

        for codon in codons:
            if codon in self.codon_table:
                aa = self.codon_table[codon]
                aa_codon_counts[aa][codon] += 1

        # Calculate relative adaptiveness for each codon
        w_values = {}
        for aa, codon_counts in aa_codon_counts.items():
            if codon_counts:
                max_count = max(codon_counts.values())
                for codon, count in codon_counts.items():
                    w_values[codon] = count / max_count

        # Calculate CAI as geometric mean of w values
        if not w_values:
            return 0.0

        log_sum = 0.0
        valid_codons = 0

        for codon in codons:
            if codon in w_values and w_values[codon] > 0:
                log_sum += math.log(w_values[codon])
                valid_codons += 1

        if valid_codons == 0:
            return 0.0

        return math.exp(log_sum / valid_codons)

    def _calculate_enc(self, codon_counts: Counter) -> float:
        """Calculate Effective Number of Codons."""
        if not codon_counts:
            return 61.0  # Maximum possible ENC

        # Group codons by amino acid
        aa_counts = defaultdict(int)
        aa_codon_counts = defaultdict(list)

        for codon, count in codon_counts.items():
            if codon in self.codon_table:
                aa = self.codon_table[codon]
                aa_counts[aa] += count
                aa_codon_counts[aa].append(count)

        # Calculate homozygosity for each amino acid
        total_homozygosity = 0.0
        total_weight = 0.0

        for aa, total_count in aa_counts.items():
            if total_count > 0:
                # Calculate homozygosity (sum of squared frequencies)
                homozygosity = sum((count / total_count) ** 2 for count in aa_codon_counts[aa])

                # Weight by amino acid frequency
                weight = total_count
                total_homozygosity += homozygosity * weight
                total_weight += weight

        if total_weight == 0:
            return 61.0

        # Calculate average homozygosity
        avg_homozygosity = total_homozygosity / total_weight

        # ENC is the reciprocal of average homozygosity
        return 1.0 / avg_homozygosity if avg_homozygosity > 0 else 61.0

    def _calculate_gc_contents(self, codons: List[str]) -> Dict[str, float]:
        """Calculate GC content at each codon position."""
        if not codons:
            return {'GC1': 0.0, 'GC2': 0.0, 'GC3': 0.0}

        pos1_bases = [codon[0] for codon in codons if len(codon) == 3]
        pos2_bases = [codon[1] for codon in codons if len(codon) == 3]
        pos3_bases = [codon[2] for codon in codons if len(codon) == 3]

        def calc_gc(bases):
            if not bases:
                return 0.0
            gc_count = sum(1 for base in bases if base in 'GC')
            return gc_count / len(bases)

        return {
            'GC1': calc_gc(pos1_bases),
            'GC2': calc_gc(pos2_bases),
            'GC3': calc_gc(pos3_bases)
        }

    def _calculate_advanced_features(self, sequences: List[Tuple[str, str]]) -> Dict[str, float]:
        """Calculate advanced codon features."""
        features = {}

        # Calculate CPB (Codon Pair Bias)
        features['CPB'] = self._calculate_cpb(sequences)

        # Calculate Consistency
        features['Consistency'] = self._calculate_consistency(sequences)

        # Calculate Remote Interaction
        features['RemoteInteraction'] = self._calculate_remote_interaction(sequences)

        # Calculate Context Bias
        features['Context_bias'] = self._calculate_context_bias(sequences)

        return features

    def _calculate_cpb(self, sequences: List[Tuple[str, str]]) -> float:
        """Calculate Codon Pair Bias."""
        if not sequences:
            return 0.0

        # Count codon pairs
        pair_counts = defaultdict(int)
        total_pairs = 0

        for seq_id, dna_seq in sequences:
            codons = extract_codons(dna_seq)
            for i in range(len(codons) - 1):
                if codons[i] in self.codon_table and codons[i+1] in self.codon_table:
                    pair = (codons[i], codons[i+1])
                    pair_counts[pair] += 1
                    total_pairs += 1

        if total_pairs == 0:
            return 0.0

        # Calculate expected frequencies based on individual codon frequencies
        codon_counts = defaultdict(int)
        for pair, count in pair_counts.items():
            codon_counts[pair[0]] += count
            codon_counts[pair[1]] += count

        total_codons = sum(codon_counts.values())

        # Calculate bias as deviation from expected
        bias_sum = 0.0
        for pair, observed in pair_counts.items():
            expected = (codon_counts[pair[0]] * codon_counts[pair[1]]) / total_codons
            if expected > 0:
                bias_sum += abs(observed - expected) / expected

        return bias_sum / len(pair_counts) if pair_counts else 0.0

    def _calculate_consistency(self, sequences: List[Tuple[str, str]]) -> float:
        """Calculate codon usage consistency across genes."""
        if len(sequences) < 2:
            return 1.0  # Perfect consistency for single sequence

        # Calculate codon usage for each sequence
        sequence_profiles = []

        for seq_id, dna_seq in sequences:
            codons = extract_codons(dna_seq)
            if len(codons) < 10:  # Skip very short sequences
                continue

            # Calculate codon frequencies for this sequence
            codon_counts = Counter(codons)
            total = sum(codon_counts.values())

            if total > 0:
                profile = np.zeros(len(self.all_codons))
                for i, codon in enumerate(self.all_codons):
                    profile[i] = codon_counts.get(codon, 0) / total
                sequence_profiles.append(profile)

        if len(sequence_profiles) < 2:
            return 1.0

        # Calculate pairwise Jensen-Shannon distances
        distances = []
        for i in range(len(sequence_profiles)):
            for j in range(i + 1, len(sequence_profiles)):
                # Add small pseudocount to avoid log(0)
                p1 = sequence_profiles[i] + 1e-10
                p2 = sequence_profiles[j] + 1e-10

                # Normalize
                p1 = p1 / np.sum(p1)
                p2 = p2 / np.sum(p2)

                # Calculate Jensen-Shannon distance
                js_dist = jensenshannon(p1, p2)
                distances.append(js_dist)

        # Consistency is 1 - average distance
        avg_distance = np.mean(distances) if distances else 0.0
        return max(0.0, 1.0 - avg_distance)

    def _calculate_remote_interaction(self, sequences: List[Tuple[str, str]]) -> float:
        """Calculate remote codon interaction metric."""
        if not sequences:
            return 0.0

        # Analyze codon co-occurrence at different distances
        interaction_scores = []

        for seq_id, dna_seq in sequences:
            codons = extract_codons(dna_seq)
            if len(codons) < 20:  # Need sufficient length for remote analysis
                continue

            # Calculate co-occurrence at different distances
            distances = [3, 6, 9, 12]  # Analyze at these codon distances
            seq_scores = []

            for dist in distances:
                cooccurrence = defaultdict(int)
                total_pairs = 0

                for i in range(len(codons) - dist):
                    if (codons[i] in self.codon_table and
                        codons[i + dist] in self.codon_table):
                        pair = tuple(sorted([codons[i], codons[i + dist]]))
                        cooccurrence[pair] += 1
                        total_pairs += 1

                if total_pairs > 0:
                    # Calculate deviation from random expectation
                    codon_freqs = Counter(codons)
                    total_codons = len(codons)

                    deviation_sum = 0.0
                    for pair, observed in cooccurrence.items():
                        expected = (codon_freqs[pair[0]] * codon_freqs[pair[1]]) / total_codons
                        if expected > 0:
                            deviation_sum += abs(observed - expected) / expected

                    seq_scores.append(deviation_sum / len(cooccurrence))

            if seq_scores:
                interaction_scores.append(np.mean(seq_scores))

        return np.mean(interaction_scores) if interaction_scores else 0.0

    def _calculate_context_bias(self, sequences: List[Tuple[str, str]]) -> float:
        """Calculate context-dependent codon bias."""
        if not sequences:
            return 0.0

        # Analyze codon usage in different contexts (5' and 3' neighbors)
        context_counts = defaultdict(lambda: defaultdict(int))

        for seq_id, dna_seq in sequences:
            codons = extract_codons(dna_seq)

            for i in range(1, len(codons) - 1):  # Skip first and last codons
                if codons[i] in self.codon_table:
                    # Create context: previous_codon + current_codon + next_codon
                    context = (codons[i-1], codons[i+1])
                    context_counts[context][codons[i]] += 1

        # Calculate bias for each context
        bias_scores = []

        for context, codon_counts in context_counts.items():
            if sum(codon_counts.values()) < 5:  # Skip rare contexts
                continue

            total = sum(codon_counts.values())

            # Group by amino acid and calculate bias
            aa_usage = defaultdict(list)
            for codon, count in codon_counts.items():
                if codon in self.codon_table:
                    aa = self.codon_table[codon]
                    aa_usage[aa].append(count / total)

            # Calculate bias within each amino acid
            context_bias = 0.0
            aa_count = 0

            for aa, freqs in aa_usage.items():
                if len(freqs) > 1:  # Only consider amino acids with multiple codons
                    # Calculate deviation from uniform distribution
                    uniform_freq = 1.0 / len(freqs)
                    bias = sum(abs(freq - uniform_freq) for freq in freqs)
                    context_bias += bias
                    aa_count += 1

            if aa_count > 0:
                bias_scores.append(context_bias / aa_count)

        return np.mean(bias_scores) if bias_scores else 0.0

    def _calculate_heg_features(self, heg_codons: List[str], bg_codons: List[str]) -> Dict[str, float]:
        """Calculate HEG-specific codon features."""
        features = {}

        if not heg_codons or not bg_codons:
            return features

        # Calculate features for HEG sequences
        heg_features = self._calculate_basic_features(heg_codons)
        for key, value in heg_features.items():
            features[f'HEG_{key}'] = value

        # Calculate features for background sequences
        bg_features = self._calculate_basic_features(bg_codons)
        for key, value in bg_features.items():
            features[f'BG_{key}'] = value

        # Calculate delta features (HEG - Background)
        for key in heg_features:
            if key in bg_features:
                features[f'delta_{key}'] = heg_features[key] - bg_features[key]

        # Calculate RSCU difference
        features['RSCU_diff'] = self._calculate_rscu_difference(heg_codons, bg_codons)

        # Calculate codon bias between HEG and background
        features['codon_bias_HEG_BP'] = self._calculate_codon_bias_heg_bg(heg_codons, bg_codons)

        # Calculate CAI using HEG as reference
        features['CAI_HEG'] = self._calculate_cai_with_reference(bg_codons, heg_codons)
        features['CAI_BP'] = self._calculate_cai_with_reference(heg_codons, bg_codons)

        return features

    def _calculate_rscu_difference(self, heg_codons: List[str], bg_codons: List[str]) -> float:
        """Calculate RSCU (Relative Synonymous Codon Usage) difference."""
        def calculate_rscu(codons):
            # Count codons by amino acid
            aa_codon_counts = defaultdict(lambda: defaultdict(int))
            for codon in codons:
                if codon in self.codon_table:
                    aa = self.codon_table[codon]
                    aa_codon_counts[aa][codon] += 1

            # Calculate RSCU values
            rscu = {}
            for aa, codon_counts in aa_codon_counts.items():
                total_aa = sum(codon_counts.values())
                num_codons = len(codon_counts)

                if total_aa > 0 and num_codons > 0:
                    for codon, count in codon_counts.items():
                        expected = total_aa / num_codons
                        rscu[codon] = (count / expected) if expected > 0 else 0.0

            return rscu

        heg_rscu = calculate_rscu(heg_codons)
        bg_rscu = calculate_rscu(bg_codons)

        # Calculate difference
        differences = []
        all_codons = set(heg_rscu.keys()) | set(bg_rscu.keys())

        for codon in all_codons:
            heg_val = heg_rscu.get(codon, 1.0)  # Default RSCU = 1.0
            bg_val = bg_rscu.get(codon, 1.0)
            differences.append(abs(heg_val - bg_val))

        return np.mean(differences) if differences else 0.0

    def _calculate_codon_bias_heg_bg(self, heg_codons: List[str], bg_codons: List[str]) -> float:
        """Calculate codon bias between HEG and background sequences."""
        if not heg_codons or not bg_codons:
            return 0.0

        # Calculate codon frequencies
        heg_counts = Counter(heg_codons)
        bg_counts = Counter(bg_codons)

        heg_total = len(heg_codons)
        bg_total = len(bg_codons)

        # Calculate Jensen-Shannon divergence
        all_codons = set(heg_counts.keys()) | set(bg_counts.keys())

        heg_freqs = np.array([heg_counts.get(codon, 0) / heg_total for codon in all_codons])
        bg_freqs = np.array([bg_counts.get(codon, 0) / bg_total for codon in all_codons])

        # Add pseudocount to avoid log(0)
        heg_freqs = heg_freqs + 1e-10
        bg_freqs = bg_freqs + 1e-10

        # Normalize
        heg_freqs = heg_freqs / np.sum(heg_freqs)
        bg_freqs = bg_freqs / np.sum(bg_freqs)

        return jensenshannon(heg_freqs, bg_freqs)

    def _calculate_cai_with_reference(self, query_codons: List[str],
                                    reference_codons: List[str]) -> float:
        """Calculate CAI using a specific reference set."""
        if not query_codons or not reference_codons:
            return 0.0

        # Calculate reference codon usage
        ref_aa_codon_counts = defaultdict(lambda: defaultdict(int))
        for codon in reference_codons:
            if codon in self.codon_table:
                aa = self.codon_table[codon]
                ref_aa_codon_counts[aa][codon] += 1

        # Calculate relative adaptiveness values from reference
        w_values = {}
        for aa, codon_counts in ref_aa_codon_counts.items():
            if codon_counts:
                max_count = max(codon_counts.values())
                for codon, count in codon_counts.items():
                    w_values[codon] = count / max_count

        # Calculate CAI for query sequences
        if not w_values:
            return 0.0

        log_sum = 0.0
        valid_codons = 0

        for codon in query_codons:
            if codon in w_values and w_values[codon] > 0:
                log_sum += math.log(w_values[codon])
                valid_codons += 1
            elif codon in self.codon_table:
                # Use small value for codons not in reference
                log_sum += math.log(0.01)
                valid_codons += 1

        if valid_codons == 0:
            return 0.0

        return math.exp(log_sum / valid_codons)
