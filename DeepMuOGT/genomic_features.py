"""
Genomic Feature Calculator for DeepMuOGT.

This module implements comprehensive genomic analysis including:
- Basic genome metrics (size, GC content)
- Nucleotide and dinucleotide frequencies
- Various skew metrics (GC skew, AT skew, etc.)
- Skew change points and structural features
- Enhanced breakpoint analysis
"""

import numpy as np
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Optional
from scipy.signal import find_peaks
import gzip

try:
    from .utils.sequence_utils import load_sequences_from_fasta, calculate_gc_content
    from .utils.logging_utils import get_logger
except ImportError:
    from utils.sequence_utils import load_sequences_from_fasta, calculate_gc_content
    from utils.logging_utils import get_logger

logger = get_logger(__name__)

class GenomicFeatureCalculator:
    """
    Calculates comprehensive genomic features for microbial growth prediction.
    
    This class analyzes genome sequences to extract features related to genome
    composition, structure, and organization that may correlate with optimal
    growth temperature and other phenotypic traits.
    """
    
    def __init__(self):
        """Initialize the genomic feature calculator."""
        self.nucleotides = ['A', 'T', 'G', 'C']
        self.dinucleotides = [n1 + n2 for n1 in self.nucleotides for n2 in self.nucleotides]
    
    def calculate_features(self, genome_file: str) -> Dict[str, float]:
        """
        Calculate all genomic features from a genome file.
        
        Args:
            genome_file: Path to genome FASTA file (can be gzipped)
        
        Returns:
            Dictionary of genomic features
        """
        try:
            # Load genome sequences
            sequences = load_sequences_from_fasta(genome_file)
            
            if not sequences:
                logger.warning(f"No sequences found in {genome_file}")
                return {}
            
            # Combine all sequences into one genome string
            genome_sequence = ''.join(seq for _, seq in sequences)
            
            if not genome_sequence:
                logger.warning("Empty genome sequence")
                return {}
            
            features = {}
            
            # Calculate basic genome metrics
            features.update(self._calculate_basic_metrics(genome_sequence))
            
            # Calculate nucleotide frequencies
            features.update(self._calculate_nucleotide_frequencies(genome_sequence))
            
            # Calculate dinucleotide frequencies
            features.update(self._calculate_dinucleotide_frequencies(genome_sequence))
            
            # Calculate skew metrics
            features.update(self._calculate_skew_metrics(genome_sequence))
            
            # Calculate skew change points
            features.update(self._calculate_skew_change_points(genome_sequence))
            
            # Calculate enhanced breakpoint features
            features.update(self._calculate_enhanced_breakpoint_features(genome_sequence))
            
            return features
            
        except Exception as e:
            logger.error(f"Error calculating genomic features from {genome_file}: {e}")
            return {}
    
    def _calculate_basic_metrics(self, genome_sequence: str) -> Dict[str, float]:
        """Calculate basic genome metrics."""
        features = {}
        
        # Genome size
        features['genome_size'] = len(genome_sequence)
        
        # Overall GC content
        features['gc_content'] = calculate_gc_content(genome_sequence)
        
        return features
    
    def _calculate_nucleotide_frequencies(self, genome_sequence: str) -> Dict[str, float]:
        """Calculate nucleotide frequencies."""
        features = {}
        
        # Count nucleotides
        nucleotide_counts = Counter(genome_sequence.upper())
        total_nucleotides = sum(nucleotide_counts[nt] for nt in self.nucleotides)
        
        if total_nucleotides == 0:
            return features
        
        # Calculate frequencies
        for nt in self.nucleotides:
            features[f'freq_{nt}'] = nucleotide_counts[nt] / total_nucleotides
        
        return features
    
    def _calculate_dinucleotide_frequencies(self, genome_sequence: str) -> Dict[str, float]:
        """Calculate dinucleotide frequencies."""
        features = {}
        
        if len(genome_sequence) < 2:
            return features
        
        # Count dinucleotides
        dinucleotide_counts = Counter()
        for i in range(len(genome_sequence) - 1):
            dinuc = genome_sequence[i:i+2].upper()
            if all(nt in self.nucleotides for nt in dinuc):
                dinucleotide_counts[dinuc] += 1
        
        total_dinucleotides = sum(dinucleotide_counts.values())
        
        if total_dinucleotides == 0:
            return features
        
        # Calculate frequencies
        for dinuc in self.dinucleotides:
            features[f'dinuc_freq_{dinuc}'] = dinucleotide_counts[dinuc] / total_dinucleotides
        
        return features
    
    def _calculate_skew_metrics(self, genome_sequence: str) -> Dict[str, float]:
        """Calculate various skew metrics."""
        features = {}
        
        # Count nucleotides
        counts = Counter(genome_sequence.upper())
        
        # GC skew: (G-C)/(G+C)
        gc_sum = counts['G'] + counts['C']
        if gc_sum > 0:
            features['gc_skew'] = (counts['G'] - counts['C']) / gc_sum
        else:
            features['gc_skew'] = 0.0
        
        # AT skew: (A-T)/(A+T)
        at_sum = counts['A'] + counts['T']
        if at_sum > 0:
            features['at_skew'] = (counts['A'] - counts['T']) / at_sum
        else:
            features['at_skew'] = 0.0
        
        # Purine-Pyrimidine skew: (A+G)-(C+T)/(A+G+C+T)
        total = counts['A'] + counts['T'] + counts['G'] + counts['C']
        if total > 0:
            purine = counts['A'] + counts['G']
            pyrimidine = counts['C'] + counts['T']
            features['purine_pyrimidine_skew'] = (purine - pyrimidine) / total
        else:
            features['purine_pyrimidine_skew'] = 0.0
        
        # Keto-Amino skew: (G+T)-(A+C)/(G+T+A+C)
        if total > 0:
            keto = counts['G'] + counts['T']
            amino = counts['A'] + counts['C']
            features['keto_amino_skew'] = (keto - amino) / total
        else:
            features['keto_amino_skew'] = 0.0
        
        # Strong-Weak skew: (G+C)-(A+T)/(G+C+A+T)
        if total > 0:
            strong = counts['G'] + counts['C']
            weak = counts['A'] + counts['T']
            features['strong_weak_skew'] = (strong - weak) / total
        else:
            features['strong_weak_skew'] = 0.0
        
        return features
    
    def _calculate_skew_change_points(self, genome_sequence: str, window_size: int = 10000) -> Dict[str, float]:
        """Calculate skew change points and related features."""
        features = {}
        
        if len(genome_sequence) < window_size * 2:
            # Genome too small for window analysis
            features['gc_skew_change_points'] = 0.0
            features['at_skew_change_points'] = 0.0
            features['gc_skew_variance'] = 0.0
            features['at_skew_variance'] = 0.0
            return features
        
        # Calculate skew in sliding windows
        gc_skews = []
        at_skews = []
        
        for i in range(0, len(genome_sequence) - window_size + 1, window_size // 2):
            window = genome_sequence[i:i + window_size].upper()
            counts = Counter(window)
            
            # GC skew
            gc_sum = counts['G'] + counts['C']
            if gc_sum > 0:
                gc_skew = (counts['G'] - counts['C']) / gc_sum
            else:
                gc_skew = 0.0
            gc_skews.append(gc_skew)
            
            # AT skew
            at_sum = counts['A'] + counts['T']
            if at_sum > 0:
                at_skew = (counts['A'] - counts['T']) / at_sum
            else:
                at_skew = 0.0
            at_skews.append(at_skew)
        
        if len(gc_skews) < 3:
            features['gc_skew_change_points'] = 0.0
            features['at_skew_change_points'] = 0.0
            features['gc_skew_variance'] = 0.0
            features['at_skew_variance'] = 0.0
            return features
        
        # Find change points (peaks and valleys)
        gc_skews_array = np.array(gc_skews)
        at_skews_array = np.array(at_skews)
        
        # Find peaks and valleys
        gc_peaks, _ = find_peaks(gc_skews_array, height=0.01)
        gc_valleys, _ = find_peaks(-gc_skews_array, height=0.01)
        at_peaks, _ = find_peaks(at_skews_array, height=0.01)
        at_valleys, _ = find_peaks(-at_skews_array, height=0.01)
        
        # Count change points
        features['gc_skew_change_points'] = len(gc_peaks) + len(gc_valleys)
        features['at_skew_change_points'] = len(at_peaks) + len(at_valleys)
        
        # Calculate variance
        features['gc_skew_variance'] = np.var(gc_skews_array)
        features['at_skew_variance'] = np.var(at_skews_array)
        
        return features

    def _calculate_enhanced_breakpoint_features(self, genome_sequence: str, window_size: int = 5000) -> Dict[str, float]:
        """Calculate enhanced breakpoint features for detecting genomic structural variations."""
        features = {}

        if len(genome_sequence) < window_size * 4:
            # Genome too small for analysis
            features['dinuc_skew_breakpoints'] = 0.0
            features['normalized_breakpoint_density'] = 0.0
            features['breakpoint_clustering_coefficient'] = 0.0
            features['compositional_heterogeneity'] = 0.0
            return features

        # Calculate dinucleotide skew breakpoints
        dinuc_skews = self._calculate_dinucleotide_skews(genome_sequence, window_size)
        features['dinuc_skew_breakpoints'] = self._count_breakpoints(dinuc_skews)

        # Calculate normalized breakpoint density
        genome_length = len(genome_sequence)
        features['normalized_breakpoint_density'] = features['dinuc_skew_breakpoints'] / (genome_length / 1000000)  # per Mb

        # Calculate breakpoint clustering coefficient
        features['breakpoint_clustering_coefficient'] = self._calculate_clustering_coefficient(dinuc_skews)

        # Calculate compositional heterogeneity
        features['compositional_heterogeneity'] = self._calculate_compositional_heterogeneity(genome_sequence, window_size)

        return features

    def _calculate_dinucleotide_skews(self, genome_sequence: str, window_size: int) -> List[float]:
        """Calculate dinucleotide skew values in sliding windows."""
        skews = []

        for i in range(0, len(genome_sequence) - window_size + 1, window_size // 2):
            window = genome_sequence[i:i + window_size].upper()

            # Calculate CG dinucleotide skew as an example
            cg_count = window.count('CG')
            gc_count = window.count('GC')
            total_cg = cg_count + gc_count

            if total_cg > 0:
                skew = (cg_count - gc_count) / total_cg
            else:
                skew = 0.0

            skews.append(skew)

        return skews

    def _count_breakpoints(self, skews: List[float], threshold: float = 0.1) -> float:
        """Count breakpoints in skew profile."""
        if len(skews) < 3:
            return 0.0

        breakpoints = 0
        for i in range(1, len(skews) - 1):
            # Check for significant change in skew
            if abs(skews[i] - skews[i-1]) > threshold or abs(skews[i+1] - skews[i]) > threshold:
                breakpoints += 1

        return float(breakpoints)

    def _calculate_clustering_coefficient(self, skews: List[float]) -> float:
        """Calculate clustering coefficient of breakpoints."""
        if len(skews) < 5:
            return 0.0

        # Find breakpoint positions
        breakpoint_positions = []
        threshold = np.std(skews) if len(skews) > 1 else 0.1

        for i in range(1, len(skews) - 1):
            if abs(skews[i] - skews[i-1]) > threshold:
                breakpoint_positions.append(i)

        if len(breakpoint_positions) < 3:
            return 0.0

        # Calculate clustering: average distance between consecutive breakpoints
        distances = []
        for i in range(1, len(breakpoint_positions)):
            distances.append(breakpoint_positions[i] - breakpoint_positions[i-1])

        if not distances:
            return 0.0

        # Clustering coefficient: inverse of coefficient of variation of distances
        mean_distance = np.mean(distances)
        std_distance = np.std(distances)

        if mean_distance > 0:
            cv = std_distance / mean_distance
            return 1.0 / (1.0 + cv)  # Higher values indicate more clustering
        else:
            return 0.0

    def _calculate_compositional_heterogeneity(self, genome_sequence: str, window_size: int) -> float:
        """Calculate compositional heterogeneity across the genome."""
        if len(genome_sequence) < window_size * 3:
            return 0.0

        # Calculate GC content in sliding windows
        gc_contents = []

        for i in range(0, len(genome_sequence) - window_size + 1, window_size):
            window = genome_sequence[i:i + window_size]
            gc_content = calculate_gc_content(window)
            gc_contents.append(gc_content)

        if len(gc_contents) < 2:
            return 0.0

        # Calculate coefficient of variation of GC content
        mean_gc = np.mean(gc_contents)
        std_gc = np.std(gc_contents)

        if mean_gc > 0:
            return std_gc / mean_gc
        else:
            return 0.0
