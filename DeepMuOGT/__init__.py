"""
DeepMuOGT - Feature Extraction for Optimal Growth Temperature Prediction

This package provides comprehensive feature extraction capabilities for microbial genomes,
specifically designed for optimal growth temperature (OGT) prediction. It extracts various
types of features including:

- Codon usage features (CUB, CPB, consistency, remote interaction)
- Amino acid composition and physicochemical properties
- Genomic features (GC content, skew metrics, genome size)
- Taxonomy features (based on NCBI taxonomy IDs)
- Pathway features (KEGG pathway completeness)
- HEG (Highly Expressed Genes) analysis features

The package is designed to work with genomic data organized in the OGT project structure.
"""

__version__ = "1.0.0"
__author__ = "DeepMuOGT Team"

# Import main feature extraction functions
from .feature_extraction import (
    extract_features_for_genome,
    extract_features_batch,
    combine_feature_files
)

# Import individual feature calculators
from .codon_features import CodonFeatureCalculator
from .amino_acid_features import AminoAcidFeatureCalculator
from .genomic_features import GenomicFeatureCalculator
from .taxonomy_features import TaxonomyFeatureCalculator
from .pathway_features import PathwayFeatureCalculator
from .heg_analysis import HEGAnalyzer

__all__ = [
    'extract_features_for_genome',
    'extract_features_batch',
    'combine_feature_files',
    'CodonFeatureCalculator',
    'AminoAcidFeatureCalculator',
    'GenomicFeatureCalculator',
    'TaxonomyFeatureCalculator',
    'PathwayFeatureCalculator',
    'HEGAnalyzer'
]
