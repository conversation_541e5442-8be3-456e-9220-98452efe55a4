"""
Pathway Feature Calculator for DeepMuOGT.

This module implements KEGG pathway analysis including:
- Pathway completeness calculation
- Functional categorization
- Metabolic capacity scoring
- Pathway enrichment analysis
"""

import os
import json
import numpy as np
from collections import defaultdict, Counter
from typing import Dict, List, Set, Optional, Tuple

try:
    from .utils.file_utils import load_ko_annotations
    from .utils.logging_utils import get_logger
except ImportError:
    from utils.file_utils import load_ko_annotations
    from utils.logging_utils import get_logger

logger = get_logger(__name__)

class PathwayDatabase:
    """
    Database for KEGG pathway information and KO mappings.
    
    This class manages the mapping between KO terms and KEGG pathways,
    providing functionality to calculate pathway completeness and features.
    """
    
    def __init__(self, kegg_map_file: Optional[str] = None):
        """
        Initialize the pathway database.
        
        Args:
            kegg_map_file: Path to KEGG mapping file (optional)
        """
        self.ko_to_pathways = defaultdict(set)
        self.pathway_to_kos = defaultdict(set)
        self.pathway_info = {}
        
        if kegg_map_file and os.path.exists(kegg_map_file):
            self._load_kegg_mappings(kegg_map_file)
        else:
            logger.warning("KEGG mapping file not provided or not found. Using default pathway mappings.")
            self._load_default_mappings()
    
    def _load_kegg_mappings(self, kegg_map_file: str):
        """Load KEGG pathway mappings from file."""
        try:
            with open(kegg_map_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        parts = line.split('\t')
                        if len(parts) >= 2:
                            ko_id = parts[0]
                            pathway_id = parts[1]
                            
                            self.ko_to_pathways[ko_id].add(pathway_id)
                            self.pathway_to_kos[pathway_id].add(ko_id)
                            
                            # Store pathway info if available
                            if len(parts) >= 3:
                                pathway_name = parts[2]
                                self.pathway_info[pathway_id] = {
                                    'name': pathway_name,
                                    'category': self._get_pathway_category(pathway_id)
                                }
            
            logger.info(f"Loaded {len(self.pathway_to_kos)} pathways from {kegg_map_file}")
            
        except Exception as e:
            logger.error(f"Error loading KEGG mappings from {kegg_map_file}: {e}")
            self._load_default_mappings()
    
    def _load_default_mappings(self):
        """Load default pathway mappings when KEGG file is not available."""
        # Define some common KEGG pathways and their representative KOs
        default_pathways = {
            'ko00010': {  # Glycolysis
                'name': 'Glycolysis / Gluconeogenesis',
                'category': 'carbohydrate',
                'kos': ['K00844', 'K01810', 'K00850', 'K01623', 'K01624', 'K00134', 'K00927']
            },
            'ko00020': {  # Citrate cycle
                'name': 'Citrate cycle (TCA cycle)',
                'category': 'carbohydrate',
                'kos': ['K01647', 'K01681', 'K01902', 'K01903', 'K00031', 'K00164', 'K00658']
            },
            'ko00190': {  # Oxidative phosphorylation
                'name': 'Oxidative phosphorylation',
                'category': 'energy',
                'kos': ['K00330', 'K00331', 'K00332', 'K02274', 'K02275', 'K02276', 'K01507']
            },
            'ko00230': {  # Purine metabolism
                'name': 'Purine metabolism',
                'category': 'nucleotide',
                'kos': ['K00759', 'K00760', 'K00088', 'K01756', 'K00939', 'K00940', 'K00941']
            },
            'ko00240': {  # Pyrimidine metabolism
                'name': 'Pyrimidine metabolism',
                'category': 'nucleotide',
                'kos': ['K00609', 'K00610', 'K00611', 'K01465', 'K01489', 'K00857', 'K00858']
            },
            'ko00250': {  # Alanine, aspartate and glutamate metabolism
                'name': 'Alanine, aspartate and glutamate metabolism',
                'category': 'amino_acid',
                'kos': ['K00813', 'K00814', 'K01914', 'K01915', 'K00265', 'K00266', 'K01580']
            },
            'ko00260': {  # Glycine, serine and threonine metabolism
                'name': 'Glycine, serine and threonine metabolism',
                'category': 'amino_acid',
                'kos': ['K00600', 'K00830', 'K01695', 'K00281', 'K00282', 'K01620', 'K00640']
            },
            'ko00270': {  # Cysteine and methionine metabolism
                'name': 'Cysteine and methionine metabolism',
                'category': 'amino_acid',
                'kos': ['K01758', 'K00789', 'K00548', 'K01739', 'K00540', 'K00541', 'K00542']
            },
            'ko00280': {  # Valine, leucine and isoleucine degradation
                'name': 'Valine, leucine and isoleucine degradation',
                'category': 'amino_acid',
                'kos': ['K00826', 'K00166', 'K01692', 'K01825', 'K00249', 'K00140', 'K00141']
            },
            'ko00290': {  # Valine, leucine and isoleucine biosynthesis
                'name': 'Valine, leucine and isoleucine biosynthesis',
                'category': 'amino_acid',
                'kos': ['K01687', 'K01703', 'K00053', 'K01652', 'K01653', 'K00826', 'K00295']
            }
        }
        
        for pathway_id, info in default_pathways.items():
            self.pathway_info[pathway_id] = {
                'name': info['name'],
                'category': info['category']
            }
            
            for ko_id in info['kos']:
                self.ko_to_pathways[ko_id].add(pathway_id)
                self.pathway_to_kos[pathway_id].add(ko_id)
        
        logger.info(f"Loaded {len(default_pathways)} default pathways")
    
    def _get_pathway_category(self, pathway_id: str) -> str:
        """Get pathway category based on pathway ID."""
        # Simple categorization based on pathway ID ranges
        if pathway_id.startswith('ko000'):
            if pathway_id in ['ko00010', 'ko00020', 'ko00030', 'ko00040', 'ko00051', 'ko00052', 'ko00053']:
                return 'carbohydrate'
            elif pathway_id in ['ko00061', 'ko00062', 'ko00071', 'ko00072', 'ko00073']:
                return 'lipid'
        elif pathway_id.startswith('ko001'):
            if pathway_id in ['ko00190', 'ko00195']:
                return 'energy'
        elif pathway_id.startswith('ko002'):
            if pathway_id in ['ko00230', 'ko00240']:
                return 'nucleotide'
            elif pathway_id.startswith('ko0025') or pathway_id.startswith('ko0026') or pathway_id.startswith('ko0027') or pathway_id.startswith('ko0028') or pathway_id.startswith('ko0029'):
                return 'amino_acid'
        elif pathway_id.startswith('ko003'):
            return 'glycan'
        elif pathway_id.startswith('ko004'):
            return 'cofactor'
        elif pathway_id.startswith('ko005'):
            return 'terpenoid'
        elif pathway_id.startswith('ko006'):
            return 'xenobiotics'
        elif pathway_id.startswith('ko009'):
            return 'secondary_metabolites'
        
        return 'other'
    
    def map_to_pathways(self, ko_terms: List[str]) -> Dict[str, float]:
        """
        Map KO terms to pathway completeness scores.
        
        Args:
            ko_terms: List of KO terms
        
        Returns:
            Dictionary mapping pathway IDs to completeness scores
        """
        ko_set = set(ko_terms)
        pathway_scores = {}
        
        for pathway_id, pathway_kos in self.pathway_to_kos.items():
            if pathway_kos:
                # Calculate completeness as proportion of KO terms present
                present_kos = ko_set.intersection(pathway_kos)
                completeness = len(present_kos) / len(pathway_kos)
                pathway_scores[pathway_id] = completeness
        
        return pathway_scores
    
    def get_pathway_categories(self) -> Dict[str, List[str]]:
        """Get pathways grouped by category."""
        categories = defaultdict(list)
        
        for pathway_id, info in self.pathway_info.items():
            category = info.get('category', 'other')
            categories[category].append(pathway_id)
        
        return dict(categories)


class PathwayFeatureCalculator:
    """
    Enhanced pathway feature calculator with comprehensive analysis capabilities.

    This class provides advanced functionality to calculate pathway features from
    pre-annotated KO terms, with support for pathway enrichment analysis,
    functional categorization, and metabolic capacity scoring.
    """

    def __init__(self, kegg_map_file: Optional[str] = None):
        """
        Initialize the pathway feature calculator.

        Args:
            kegg_map_file: Path to KEGG mapping file (optional)
        """
        self.pathway_db = PathwayDatabase(kegg_map_file)

        # Define pathway categories for summary features
        self.pathway_categories = {
            'carbohydrate': [
                'ko00010', 'ko00020', 'ko00030', 'ko00040', 'ko00051', 'ko00052', 'ko00053',
                'ko00500', 'ko00520', 'ko00620', 'ko00630', 'ko00640', 'ko00650', 'ko00660'
            ],
            'energy': [
                'ko00190', 'ko00195', 'ko00680', 'ko00720'
            ],
            'lipid': [
                'ko00061', 'ko00062', 'ko00071', 'ko00072', 'ko00073', 'ko00100', 'ko00120',
                'ko00121', 'ko00140', 'ko00561', 'ko00564', 'ko00565', 'ko00590', 'ko00591',
                'ko00592'
            ],
            'nucleotide': [
                'ko00230', 'ko00240'
            ],
            'amino_acid': [
                'ko00250', 'ko00260', 'ko00270', 'ko00280', 'ko00290', 'ko00300', 'ko00310',
                'ko00220', 'ko00330', 'ko00340', 'ko00350', 'ko00360', 'ko00380', 'ko00400',
                'ko00410', 'ko00430', 'ko00440', 'ko00450', 'ko00460', 'ko00470', 'ko00480'
            ],
            'glycan': [
                'ko00510', 'ko00513', 'ko00514', 'ko00515', 'ko00520', 'ko00521', 'ko00523',
                'ko00524', 'ko00531', 'ko00532', 'ko00533', 'ko00534', 'ko00535', 'ko00536',
                'ko00537', 'ko00540', 'ko00541', 'ko00542', 'ko00543', 'ko00544', 'ko00545',
                'ko00546', 'ko00550', 'ko00552', 'ko00553', 'ko00571', 'ko00572', 'ko00601',
                'ko00603', 'ko00604'
            ],
            'cofactor': [
                'ko00730', 'ko00740', 'ko00750', 'ko00760', 'ko00770', 'ko00780', 'ko00785',
                'ko00790', 'ko00670', 'ko00830', 'ko00860', 'ko00130'
            ],
            'terpenoid': [
                'ko00900', 'ko00902', 'ko00903', 'ko00904', 'ko00906', 'ko00908', 'ko00909',
                'ko00981'
            ],
            'xenobiotics': [
                'ko00980', 'ko00982', 'ko00983', 'ko00984', 'ko00985', 'ko00986', 'ko00987',
                'ko00988', 'ko00989', 'ko00990', 'ko00991', 'ko00992', 'ko00993', 'ko00994',
                'ko00995', 'ko00996', 'ko00997', 'ko00998', 'ko00999'
            ],
            'secondary_metabolites': [
                'ko00940', 'ko00941', 'ko00942', 'ko00943', 'ko00944', 'ko00945', 'ko00950',
                'ko00960', 'ko00965', 'ko00966'
            ]
        }

    def calculate_features(self, ko_file: str) -> Dict[str, float]:
        """
        Calculate pathway features from a KO annotation file.

        Args:
            ko_file: Path to KO annotation file

        Returns:
            Dictionary of pathway features
        """
        try:
            # Load KO annotations
            ko_annotations = load_ko_annotations(ko_file)

            if not ko_annotations:
                logger.warning(f"No KO annotations found in {ko_file}")
                return {}

            # Extract KO terms
            ko_terms = list(ko_annotations.values())

            return self.calculate_features_from_ko_terms(ko_terms)

        except Exception as e:
            logger.error(f"Error calculating pathway features from {ko_file}: {e}")
            return {}

    def calculate_features_from_ko_terms(self, ko_terms: List[str]) -> Dict[str, float]:
        """
        Calculate pathway features from KO terms.

        Args:
            ko_terms: List of KO terms

        Returns:
            Dictionary of pathway features
        """
        if not ko_terms:
            logger.warning("No KO terms provided")
            return {}

        features = {}

        # Calculate basic pathway completeness
        pathway_scores = self.pathway_db.map_to_pathways(ko_terms)

        # Add individual pathway features
        for pathway_id, score in pathway_scores.items():
            features[pathway_id] = score

        # Calculate category-level features
        category_features = self._calculate_category_features(pathway_scores)
        features.update(category_features)

        # Calculate pathway completeness analysis
        completeness_features = self._analyze_pathway_completeness(ko_terms)
        features.update(completeness_features)

        return features

    def _calculate_category_features(self, pathway_scores: Dict[str, float]) -> Dict[str, float]:
        """Calculate pathway category summary features."""
        features = {}

        for category, pathway_ids in self.pathway_categories.items():
            # Get scores for pathways in this category
            category_scores = []
            for pathway_id in pathway_ids:
                if pathway_id in pathway_scores:
                    category_scores.append(pathway_scores[pathway_id])

            if category_scores:
                # Calculate summary statistics
                features[f'category_{category}_mean'] = np.mean(category_scores)
                features[f'category_{category}_max'] = np.max(category_scores)
                features[f'category_{category}_std'] = np.std(category_scores)
                features[f'category_{category}_count'] = len([s for s in category_scores if s > 0.1])
            else:
                features[f'category_{category}_mean'] = 0.0
                features[f'category_{category}_max'] = 0.0
                features[f'category_{category}_std'] = 0.0
                features[f'category_{category}_count'] = 0.0

        return features

    def _analyze_pathway_completeness(self, ko_terms: List[str]) -> Dict[str, float]:
        """Analyze pathway completeness in detail."""
        features = {}

        # Convert KO terms to a set for faster lookups
        ko_set = set(ko_terms)

        # Calculate pathway features
        pathway_scores = self.pathway_db.map_to_pathways(ko_terms)

        # Analyze completeness of each pathway
        completeness_info = {}

        for pathway_id, pathway_kos in self.pathway_db.pathway_to_kos.items():
            if pathway_kos:
                # Find present and missing KOs
                present_kos = ko_set.intersection(pathway_kos)
                missing_kos = pathway_kos - ko_set

                # Get pathway score
                score = pathway_scores.get(pathway_id, 0.0)

                # Calculate completeness metrics
                total_kos = len(pathway_kos)

                if total_kos > 0:
                    completeness = float(len(present_kos)) / float(total_kos)

                    # Store completeness information
                    completeness_info[pathway_id] = {
                        'name': self.pathway_db.pathway_info.get(pathway_id, {}).get('name', pathway_id),
                        'category': self.pathway_db.pathway_info.get(pathway_id, {}).get('category', 'Unknown'),
                        'completeness': completeness,
                        'score': float(score),
                        'present_kos': len(present_kos),
                        'missing_kos': len(missing_kos),
                        'total_kos': total_kos
                    }

        # Calculate summary statistics
        if completeness_info:
            completeness_values = [info['completeness'] for info in completeness_info.values()]
            features['pathway_completeness_mean'] = np.mean(completeness_values)
            features['pathway_completeness_std'] = np.std(completeness_values)
            features['pathway_completeness_max'] = np.max(completeness_values)
            features['pathways_above_50pct'] = len([c for c in completeness_values if c > 0.5])
            features['pathways_above_80pct'] = len([c for c in completeness_values if c > 0.8])
            features['total_pathways_analyzed'] = len(completeness_info)
        else:
            features['pathway_completeness_mean'] = 0.0
            features['pathway_completeness_std'] = 0.0
            features['pathway_completeness_max'] = 0.0
            features['pathways_above_50pct'] = 0.0
            features['pathways_above_80pct'] = 0.0
            features['total_pathways_analyzed'] = 0.0

        return features

    def get_pathway_enrichment(self, ko_terms: List[str], background_kos: Optional[List[str]] = None) -> Dict[str, Dict]:
        """
        Calculate pathway enrichment analysis.

        Args:
            ko_terms: List of KO terms to analyze
            background_kos: Background KO terms for comparison (optional)

        Returns:
            Dictionary with enrichment results for each pathway
        """
        if not ko_terms:
            return {}

        ko_set = set(ko_terms)

        # Use all known KOs as background if not provided
        if background_kos is None:
            background_set = set()
            for pathway_kos in self.pathway_db.pathway_to_kos.values():
                background_set.update(pathway_kos)
        else:
            background_set = set(background_kos)

        enrichment_results = {}

        for pathway_id, pathway_kos in self.pathway_db.pathway_to_kos.items():
            if not pathway_kos:
                continue

            # Calculate overlap
            overlap = ko_set.intersection(pathway_kos)

            # Calculate enrichment metrics
            pathway_size = len(pathway_kos)
            query_size = len(ko_set)
            overlap_size = len(overlap)
            background_size = len(background_set)

            if pathway_size > 0 and query_size > 0:
                # Calculate enrichment ratio
                expected = (pathway_size * query_size) / background_size if background_size > 0 else 0
                enrichment_ratio = overlap_size / expected if expected > 0 else 0

                # Calculate coverage
                coverage = overlap_size / pathway_size

                enrichment_results[pathway_id] = {
                    'pathway_name': self.pathway_db.pathway_info.get(pathway_id, {}).get('name', pathway_id),
                    'category': self.pathway_db.pathway_info.get(pathway_id, {}).get('category', 'Unknown'),
                    'overlap_size': overlap_size,
                    'pathway_size': pathway_size,
                    'query_size': query_size,
                    'enrichment_ratio': enrichment_ratio,
                    'coverage': coverage,
                    'overlapping_kos': list(overlap)
                }

        return enrichment_results
