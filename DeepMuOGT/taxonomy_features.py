"""
Taxonomy Feature Calculator for DeepMuOGT.

This module implements taxonomy-based feature extraction using NCBI taxonomy IDs.
It provides features at different taxonomic levels (phylum, class, order, family, genus)
both as raw taxonomy IDs and normalized values for machine learning.
"""

import numpy as np
from typing import Dict, Union, Optional
import logging

try:
    from .utils.logging_utils import get_logger
except ImportError:
    from utils.logging_utils import get_logger

logger = get_logger(__name__)

class TaxonomyFeatureCalculator:
    """
    Calculates taxonomy features from NCBI taxonomy IDs.
    
    This class extracts taxonomic information and converts it into features
    suitable for machine learning models. It handles missing taxonomy data
    gracefully and provides both raw and normalized taxonomy features.
    """
    
    def __init__(self):
        """Initialize the taxonomy feature calculator."""
        self.taxonomy_levels = ['phylum', 'class', 'order', 'family', 'genus']
        
        # Try to import ete3 for taxonomy lookups
        self.has_ete3 = False
        try:
            from ete3 import NCBITaxa
            self.ncbi = NCBITaxa()
            self.has_ete3 = True
            logger.info("ETE3 NCBITaxa database loaded successfully")
        except ImportError:
            logger.warning("ETE3 not available. Using simplified taxonomy features.")
            self.ncbi = None
        except Exception as e:
            logger.warning(f"Error loading NCBITaxa database: {e}. Using simplified taxonomy features.")
            self.ncbi = None
    
    def calculate_features(self, taxid: Union[str, int]) -> Dict[str, Union[int, float]]:
        """
        Calculate taxonomy features from a taxonomy ID.
        
        Args:
            taxid: NCBI taxonomy ID (can be string or integer)
        
        Returns:
            Dictionary of taxonomy features
        """
        if not taxid:
            return self._get_empty_features()
        
        # Convert taxid to int if it's a string
        if isinstance(taxid, str):
            try:
                taxid = int(taxid)
            except ValueError:
                logger.warning(f"Invalid taxonomy ID: {taxid}")
                return self._get_empty_features()
        
        if self.has_ete3 and self.ncbi:
            return self._calculate_features_with_ete3(taxid)
        else:
            return self._calculate_features_simple(taxid)
    
    def _calculate_features_with_ete3(self, taxid: int) -> Dict[str, Union[int, float]]:
        """Calculate taxonomy features using ETE3 NCBITaxa database."""
        try:
            # Get taxonomy lineage
            lineage = self._get_lineage(taxid)
            if not lineage:
                return self._get_empty_features()
            
            # Create features dictionary
            features = {}
            
            # Add taxid values for each taxonomic level
            for level in self.taxonomy_levels:
                if level in lineage and lineage[level] > 0:
                    # Add the raw taxid value
                    features[f'taxid_{level}'] = lineage[level]
                    
                    # Add a normalized value (using log scale to handle large taxid values)
                    # This can be useful for machine learning models
                    features[f'taxid_{level}_norm'] = np.log1p(lineage[level]) / 20.0  # log(10^9) ≈ 20
                else:
                    features[f'taxid_{level}'] = 0
                    features[f'taxid_{level}_norm'] = 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"Error calculating taxonomy features for taxid {taxid}: {e}")
            return self._get_empty_features()
    
    def _calculate_features_simple(self, taxid: int) -> Dict[str, Union[int, float]]:
        """Calculate simplified taxonomy features without ETE3."""
        features = {}
        
        # Use simple hash-based approach for consistent feature generation
        # This is a fallback when ETE3 is not available
        for i, level in enumerate(self.taxonomy_levels):
            # Generate pseudo-taxonomy IDs based on the original taxid
            # This ensures consistency across runs
            level_taxid = (taxid + i * 1000) % 100000  # Keep values reasonable
            
            features[f'taxid_{level}'] = level_taxid
            features[f'taxid_{level}_norm'] = np.log1p(level_taxid) / 12.0  # log(100000) ≈ 12
        
        logger.info(f"Generated simplified taxonomy features for taxid {taxid}")
        return features
    
    def _get_lineage(self, taxid: int) -> Optional[Dict[str, int]]:
        """Get the taxonomic lineage for a taxonomy ID."""
        if not self.has_ete3 or not self.ncbi:
            return None
        
        try:
            # Get the lineage
            lineage = self.ncbi.get_lineage(taxid)
            
            # Get the ranks
            ranks2levels = self.ncbi.get_rank(lineage)
            levels2ranks = {ranks2levels[t]: t for t in lineage}
            
            # Extract the taxids for the taxonomy levels we're interested in
            result = {}
            for level in self.taxonomy_levels:
                if level in levels2ranks:
                    result[level] = levels2ranks[level]
                else:
                    result[level] = 0
            
            return result
            
        except Exception as e:
            logger.error(f"Error retrieving taxonomy lineage for taxid {taxid}: {e}")
            return None
    
    def _get_empty_features(self) -> Dict[str, Union[int, float]]:
        """Get empty/default taxonomy features."""
        features = {}
        for level in self.taxonomy_levels:
            features[f'taxid_{level}'] = 0
            features[f'taxid_{level}_norm'] = 0.0
        return features
    
    def get_taxonomy_string(self, taxid: int) -> str:
        """
        Get a pipe-separated taxonomy string for a taxonomy ID.
        
        Args:
            taxid: NCBI taxonomy ID
        
        Returns:
            Pipe-separated taxonomy string (phylum|class|order|family|genus)
        """
        if not self.has_ete3 or not self.ncbi:
            # Return simplified taxonomy string
            tax_ids = []
            for i, level in enumerate(self.taxonomy_levels):
                level_taxid = (taxid + i * 1000) % 100000
                tax_ids.append(str(level_taxid))
            return "|".join(tax_ids)
        
        try:
            # Get the lineage
            lineage = self._get_lineage(taxid)
            if not lineage:
                return "|".join(["0"] * len(self.taxonomy_levels))
            
            # Extract the taxids for the taxonomy levels we're interested in
            tax_ids = []
            for level in self.taxonomy_levels:
                if level in lineage and lineage[level] > 0:
                    tax_ids.append(str(lineage[level]))
                else:
                    tax_ids.append("0")  # Use 0 for missing ranks
            
            # Return pipe-separated string
            return "|".join(tax_ids)
            
        except Exception as e:
            logger.error(f"Error retrieving taxonomy string for taxid {taxid}: {e}")
            return "|".join(["0"] * len(self.taxonomy_levels))
    
    def validate_taxid(self, taxid: Union[str, int]) -> bool:
        """
        Validate if a taxonomy ID is valid.
        
        Args:
            taxid: NCBI taxonomy ID to validate
        
        Returns:
            True if valid, False otherwise
        """
        if not taxid:
            return False
        
        # Convert to int
        try:
            taxid_int = int(taxid)
        except (ValueError, TypeError):
            return False
        
        # Basic validation: should be positive
        if taxid_int <= 0:
            return False
        
        # If ETE3 is available, try to get lineage
        if self.has_ete3 and self.ncbi:
            try:
                lineage = self.ncbi.get_lineage(taxid_int)
                return len(lineage) > 0
            except Exception:
                return False
        
        # If ETE3 is not available, assume valid if it's a positive integer
        return True
    
    def get_kingdom_from_taxid(self, taxid: Union[str, int]) -> Optional[str]:
        """
        Get the kingdom name from a taxonomy ID.
        
        Args:
            taxid: NCBI taxonomy ID
        
        Returns:
            Kingdom name or None if not found
        """
        if not self.has_ete3 or not self.ncbi:
            # Simple heuristic based on taxid ranges (very approximate)
            try:
                taxid_int = int(taxid)
                if taxid_int < 100000:
                    return "Bacteria"
                elif taxid_int < 200000:
                    return "Archaea"
                elif taxid_int < 500000:
                    return "Fungi"
                else:
                    return "Eukaryota"
            except (ValueError, TypeError):
                return None
        
        try:
            # Get the lineage
            lineage = self.ncbi.get_lineage(int(taxid))
            ranks = self.ncbi.get_rank(lineage)
            
            # Look for kingdom in the lineage
            for taxid_in_lineage in lineage:
                if ranks.get(taxid_in_lineage) == 'superkingdom':
                    names = self.ncbi.get_taxid_translator([taxid_in_lineage])
                    return names.get(taxid_in_lineage, "Unknown")
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting kingdom for taxid {taxid}: {e}")
            return None
