"""
Amino Acid Feature Calculator for DeepMuOGT.

This module implements comprehensive amino acid analysis including:
- Amino acid composition
- Physicochemical properties (hydrophobicity, charge, polarity)
- Carbon and nitrogen content analysis
- HEG vs Background analysis
- Amino Acid Adaptation Index (AAAI)
"""

import numpy as np
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Set, Optional

try:
    from .utils.sequence_utils import translate_dna_to_protein
    from .utils.logging_utils import get_logger
except ImportError:
    from utils.sequence_utils import translate_dna_to_protein
    from utils.logging_utils import get_logger

logger = get_logger(__name__)

class AminoAcidFeatureCalculator:
    """
    Calculates comprehensive amino acid features for microbial growth prediction.
    
    This class analyzes protein sequences to extract features related to amino acid
    composition, physicochemical properties, and metabolic costs.
    """
    
    def __init__(self, genetic_code: int = 11, use_heg_features: bool = True):
        """
        Initialize the amino acid feature calculator.
        
        Args:
            genetic_code: Genetic code table ID (default: 11 for bacterial)
            use_heg_features: Whether to calculate HEG-specific features
        """
        self.genetic_code = genetic_code
        self.use_heg_features = use_heg_features
        
        # Standard amino acids
        self.amino_acids = "ACDEFGHIKLMNPQRSTVWY"
        
        # Amino acid properties
        self.aa_properties = {
            'A': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 89.1},
            'C': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'molecular_weight': 121.0},
            'D': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': -1, 'molecular_weight': 133.1},
            'E': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': -1, 'molecular_weight': 147.1},
            'F': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 165.2},
            'G': {'hydrophobic': False, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 75.1},
            'H': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1, 'molecular_weight': 155.2},
            'I': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 131.2},
            'K': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1, 'molecular_weight': 146.2},
            'L': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 131.2},
            'M': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 149.2},
            'N': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'molecular_weight': 132.1},
            'P': {'hydrophobic': False, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 115.1},
            'Q': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'molecular_weight': 146.2},
            'R': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1, 'molecular_weight': 174.2},
            'S': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'molecular_weight': 105.1},
            'T': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'molecular_weight': 119.1},
            'V': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 117.1},
            'W': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'molecular_weight': 204.2},
            'Y': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'molecular_weight': 181.2}
        }
        
        # Carbon and nitrogen content per amino acid (side chain)
        self.aa_carbon_content = {
            'A': 1, 'C': 1, 'D': 2, 'E': 3, 'F': 7, 'G': 0, 'H': 4, 'I': 4, 'K': 4,
            'L': 4, 'M': 3, 'N': 2, 'P': 3, 'Q': 3, 'R': 4, 'S': 1, 'T': 2, 'V': 3,
            'W': 9, 'Y': 7
        }
        
        self.aa_nitrogen_content = {
            'A': 0, 'C': 0, 'D': 0, 'E': 0, 'F': 0, 'G': 0, 'H': 2, 'I': 0, 'K': 1,
            'L': 0, 'M': 0, 'N': 1, 'P': 0, 'Q': 1, 'R': 3, 'S': 0, 'T': 0, 'V': 0,
            'W': 1, 'Y': 0
        }
        
        # Total carbon and nitrogen (including backbone)
        self.aa_total_carbon = {aa: self.aa_carbon_content[aa] + 2 for aa in self.amino_acids}  # +2 for backbone
        self.aa_total_nitrogen = {aa: self.aa_nitrogen_content[aa] + 1 for aa in self.amino_acids}  # +1 for backbone
    
    def calculate_features(self, sequences: List[Tuple[str, str]], 
                         heg_sequences: Optional[Set[str]] = None,
                         from_dna: bool = False) -> Dict[str, float]:
        """
        Calculate all amino acid features from protein sequences.
        
        Args:
            sequences: List of (sequence_id, sequence) tuples
            heg_sequences: Set of sequence IDs that are highly expressed genes
            from_dna: Whether sequences are DNA (need translation) or protein
        
        Returns:
            Dictionary of amino acid features
        """
        if not sequences:
            logger.warning("No sequences provided for amino acid analysis")
            return {}
        
        # Convert DNA to protein if needed
        if from_dna:
            protein_sequences = []
            for seq_id, dna_seq in sequences:
                protein_seq = translate_dna_to_protein(dna_seq, self.genetic_code)
                if protein_seq:
                    protein_sequences.append((seq_id, protein_seq))
            sequences = protein_sequences
        
        if not sequences:
            logger.warning("No valid protein sequences after translation")
            return {}
        
        features = {}
        
        # Extract all amino acids
        all_aa_sequences = []
        heg_aa_sequences = []
        bg_aa_sequences = []
        
        for seq_id, protein_seq in sequences:
            # Clean sequence (remove stop codons and invalid characters)
            clean_seq = ''.join(aa for aa in protein_seq.upper() if aa in self.amino_acids)
            if clean_seq:
                all_aa_sequences.append(clean_seq)
                
                if self.use_heg_features and heg_sequences:
                    if seq_id in heg_sequences:
                        heg_aa_sequences.append(clean_seq)
                    else:
                        bg_aa_sequences.append(clean_seq)
        
        if not all_aa_sequences:
            logger.warning("No valid amino acid sequences found")
            return {}
        
        # Calculate basic amino acid features
        features.update(self._calculate_basic_features(all_aa_sequences))
        
        # Calculate physicochemical properties
        features.update(self._calculate_physicochemical_features(all_aa_sequences))
        
        # Calculate carbon and nitrogen features
        features.update(self._calculate_carbon_nitrogen_features(all_aa_sequences))
        
        # Calculate HEG-specific features if requested
        if self.use_heg_features and heg_sequences and heg_aa_sequences and bg_aa_sequences:
            features.update(self._calculate_heg_features(heg_aa_sequences, bg_aa_sequences))
        
        return features
    
    def _calculate_basic_features(self, sequences: List[str]) -> Dict[str, float]:
        """Calculate basic amino acid composition features."""
        features = {}
        
        # Count all amino acids
        aa_counts = Counter()
        total_count = 0
        
        for sequence in sequences:
            for aa in sequence:
                if aa in self.amino_acids:
                    aa_counts[aa] += 1
                    total_count += 1
        
        if total_count == 0:
            return features
        
        # Calculate amino acid frequencies
        for aa in self.amino_acids:
            features[f'aa_freq_{aa}'] = aa_counts[aa] / total_count
        
        return features

    def _calculate_physicochemical_features(self, sequences: List[str]) -> Dict[str, float]:
        """Calculate physicochemical property features."""
        features = {}

        # Count amino acids by properties
        total_count = 0
        hydrophobic_count = 0
        polar_count = 0
        charged_count = 0
        positive_count = 0
        negative_count = 0

        for sequence in sequences:
            for aa in sequence:
                if aa in self.aa_properties:
                    total_count += 1
                    props = self.aa_properties[aa]

                    if props['hydrophobic']:
                        hydrophobic_count += 1
                    if props['polar']:
                        polar_count += 1
                    if props['charged']:
                        charged_count += 1
                        if props['charge'] > 0:
                            positive_count += 1
                        elif props['charge'] < 0:
                            negative_count += 1

        if total_count == 0:
            return features

        # Calculate proportions
        features['aa_prop_hydrophobic'] = hydrophobic_count / total_count
        features['aa_prop_polar'] = polar_count / total_count
        features['aa_prop_charged'] = charged_count / total_count
        features['aa_prop_positive'] = positive_count / total_count
        features['aa_prop_negative'] = negative_count / total_count

        # Calculate ratios
        if negative_count > 0:
            features['aa_charge_ratio'] = positive_count / negative_count
        else:
            features['aa_charge_ratio'] = float(positive_count) if positive_count > 0 else 0.0

        if polar_count > 0:
            features['aa_hydrophobicity_ratio'] = hydrophobic_count / polar_count
        else:
            features['aa_hydrophobicity_ratio'] = float(hydrophobic_count) if hydrophobic_count > 0 else 0.0

        return features

    def _calculate_carbon_nitrogen_features(self, sequences: List[str]) -> Dict[str, float]:
        """Calculate carbon and nitrogen content features."""
        features = {}

        total_carbon = 0
        total_nitrogen = 0
        total_carbon_arsc = 0  # Side chain carbon
        total_nitrogen_arsc = 0  # Side chain nitrogen
        total_count = 0

        for sequence in sequences:
            for aa in sequence:
                if aa in self.amino_acids:
                    total_count += 1
                    total_carbon += self.aa_total_carbon[aa]
                    total_nitrogen += self.aa_total_nitrogen[aa]
                    total_carbon_arsc += self.aa_carbon_content[aa]
                    total_nitrogen_arsc += self.aa_nitrogen_content[aa]

        if total_count == 0:
            return features

        # Calculate average values
        features['aa_carbon_total'] = total_carbon / total_count
        features['aa_nitrogen_total'] = total_nitrogen / total_count
        features['aa_c_arsc'] = total_carbon_arsc / total_count
        features['aa_n_arsc'] = total_nitrogen_arsc / total_count

        # Calculate ratios
        if total_nitrogen > 0:
            features['aa_carbon_nitrogen_ratio'] = total_carbon / total_nitrogen
        else:
            features['aa_carbon_nitrogen_ratio'] = 0.0

        if total_nitrogen_arsc > 0:
            features['aa_c_arsc_n_arsc_ratio'] = total_carbon_arsc / total_nitrogen_arsc
        else:
            features['aa_c_arsc_n_arsc_ratio'] = 0.0

        return features

    def _calculate_heg_features(self, heg_sequences: List[str], bg_sequences: List[str]) -> Dict[str, float]:
        """Calculate HEG-specific amino acid features."""
        features = {}

        if not heg_sequences or not bg_sequences:
            return features

        # Calculate features for HEG sequences
        heg_basic = self._calculate_basic_features(heg_sequences)
        heg_phys = self._calculate_physicochemical_features(heg_sequences)
        heg_cn = self._calculate_carbon_nitrogen_features(heg_sequences)

        # Calculate features for background sequences
        bg_basic = self._calculate_basic_features(bg_sequences)
        bg_phys = self._calculate_physicochemical_features(bg_sequences)
        bg_cn = self._calculate_carbon_nitrogen_features(bg_sequences)

        # Add HEG features with prefix
        for key, value in heg_basic.items():
            features[f'HEG_{key}'] = value
        for key, value in heg_phys.items():
            features[f'HEG_{key}'] = value
        for key, value in heg_cn.items():
            features[f'HEG_{key}'] = value

        # Add background features with prefix
        for key, value in bg_basic.items():
            features[f'BG_{key}'] = value
        for key, value in bg_phys.items():
            features[f'BG_{key}'] = value
        for key, value in bg_cn.items():
            features[f'BG_{key}'] = value

        # Calculate delta features (HEG - Background)
        all_keys = set(heg_basic.keys()) | set(heg_phys.keys()) | set(heg_cn.keys())
        heg_all = {**heg_basic, **heg_phys, **heg_cn}
        bg_all = {**bg_basic, **bg_phys, **bg_cn}

        for key in all_keys:
            if key in heg_all and key in bg_all:
                features[f'delta_{key}'] = heg_all[key] - bg_all[key]

        # Calculate AAAI (Amino Acid Adaptation Index)
        features['AAAI'] = self._calculate_aaai(bg_sequences, heg_sequences)

        # Calculate amino acid bias between HEG and background
        features['aa_bias_HEG_BP'] = self._calculate_aa_bias_heg_bg(heg_sequences, bg_sequences)

        return features

    def _calculate_aaai(self, query_sequences: List[str], reference_sequences: List[str]) -> float:
        """Calculate Amino Acid Adaptation Index using reference sequences."""
        if not query_sequences or not reference_sequences:
            return 0.0

        # Calculate reference amino acid usage
        ref_aa_counts = Counter()
        for sequence in reference_sequences:
            for aa in sequence:
                if aa in self.amino_acids:
                    ref_aa_counts[aa] += 1

        ref_total = sum(ref_aa_counts.values())
        if ref_total == 0:
            return 0.0

        # Calculate relative adaptiveness values
        max_freq = max(ref_aa_counts.values()) if ref_aa_counts else 1
        w_values = {}
        for aa in self.amino_acids:
            w_values[aa] = ref_aa_counts[aa] / max_freq if max_freq > 0 else 0.0

        # Calculate AAAI for query sequences
        log_sum = 0.0
        valid_aa_count = 0

        for sequence in query_sequences:
            for aa in sequence:
                if aa in self.amino_acids and w_values[aa] > 0:
                    log_sum += np.log(w_values[aa])
                    valid_aa_count += 1
                elif aa in self.amino_acids:
                    # Use small value for amino acids not in reference
                    log_sum += np.log(0.01)
                    valid_aa_count += 1

        if valid_aa_count == 0:
            return 0.0

        return np.exp(log_sum / valid_aa_count)

    def _calculate_aa_bias_heg_bg(self, heg_sequences: List[str], bg_sequences: List[str]) -> float:
        """Calculate amino acid bias between HEG and background sequences."""
        if not heg_sequences or not bg_sequences:
            return 0.0

        # Calculate amino acid frequencies
        heg_counts = Counter()
        bg_counts = Counter()

        for sequence in heg_sequences:
            for aa in sequence:
                if aa in self.amino_acids:
                    heg_counts[aa] += 1

        for sequence in bg_sequences:
            for aa in sequence:
                if aa in self.amino_acids:
                    bg_counts[aa] += 1

        heg_total = sum(heg_counts.values())
        bg_total = sum(bg_counts.values())

        if heg_total == 0 or bg_total == 0:
            return 0.0

        # Calculate frequencies and Jensen-Shannon divergence
        heg_freqs = np.array([heg_counts[aa] / heg_total for aa in self.amino_acids])
        bg_freqs = np.array([bg_counts[aa] / bg_total for aa in self.amino_acids])

        # Add pseudocount to avoid log(0)
        heg_freqs = heg_freqs + 1e-10
        bg_freqs = bg_freqs + 1e-10

        # Normalize
        heg_freqs = heg_freqs / np.sum(heg_freqs)
        bg_freqs = bg_freqs / np.sum(bg_freqs)

        # Calculate Jensen-Shannon distance
        from scipy.spatial.distance import jensenshannon
        return jensenshannon(heg_freqs, bg_freqs)
