#!/usr/bin/env python3
"""
Test script for DeepMuOGT feature extraction system.

This script tests the feature extraction system using the metadata_copy.csv file
and the available genomic data in the OGT project structure.
"""

import os
import sys
import pandas as pd
from pathlib import Path

# Add DeepMuOGT to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from feature_extraction import extract_features_for_genome, extract_features_batch
from utils.logging_utils import setup_logging, get_logger

def test_single_genome():
    """Test feature extraction for a single genome."""
    logger = get_logger(__name__)
    logger.info("Testing single genome feature extraction...")
    
    # Test with the first genome from metadata
    metadata_df = pd.read_csv('../metadata_copy.csv')
    test_genome = metadata_df.iloc[0]
    
    genome_id = test_genome['genome_id']
    taxid = test_genome['taxid']
    
    logger.info(f"Testing with genome: {genome_id}, taxid: {taxid}")
    
    # Define base directories
    base_dirs = {
        'genome_dir': '..',  # Parent directory contains Bacteria, Archaea, etc.
        'cds_dir': '../CDS',
        'protein_dir': '../protein',
        'trna_dir': '../tRNA',
        'rrna_dir': '../rRNA'
    }
    
    try:
        # Extract features
        features = extract_features_for_genome(
            genome_id=genome_id,
            base_dirs=base_dirs,
            taxid=taxid,
            kegg_map_file=None,  # Use default mappings
            heg_ko_file=None,    # Use default HEG KOs
            advanced_codon_features=True,
            genetic_code=11
        )
        
        if features:
            logger.info(f"Successfully extracted {len(features)} features for {genome_id}")
            
            # Print some example features
            logger.info("Sample features:")
            for i, (key, value) in enumerate(features.items()):
                if i < 10:  # Show first 10 features
                    logger.info(f"  {key}: {value}")
                else:
                    break
            
            return True
        else:
            logger.error(f"No features extracted for {genome_id}")
            return False
            
    except Exception as e:
        logger.error(f"Error testing single genome: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def test_batch_extraction():
    """Test batch feature extraction with a subset of genomes."""
    logger = get_logger(__name__)
    logger.info("Testing batch feature extraction...")
    
    # Create a test metadata file with first 3 genomes
    metadata_df = pd.read_csv('../metadata_copy.csv')
    test_metadata = metadata_df.head(3)  # Test with first 3 genomes
    
    test_metadata_file = 'test_metadata.csv'
    test_metadata.to_csv(test_metadata_file, index=False)
    
    logger.info(f"Created test metadata file with {len(test_metadata)} genomes")
    
    # Define base directories
    base_dirs = {
        'genome_dir': '..',
        'cds_dir': '../CDS',
        'protein_dir': '../protein',
        'trna_dir': '../tRNA',
        'rrna_dir': '../rRNA'
    }
    
    try:
        # Extract features for test genomes
        extract_features_batch(
            metadata_file=test_metadata_file,
            output_dir='test_features',
            base_dirs=base_dirs,
            kegg_map_file=None,
            heg_ko_file=None,
            advanced_codon_features=True,
            num_processes=1,  # Use single process for testing
            genetic_code=11
        )
        
        # Check if feature files were created
        feature_files = list(Path('test_features').glob('*_features.npz'))
        logger.info(f"Created {len(feature_files)} feature files")
        
        if len(feature_files) > 0:
            # Test loading a feature file
            import numpy as np
            test_file = feature_files[0]
            with np.load(test_file) as data:
                logger.info(f"Test feature file {test_file.name} contains {len(data.files)} features")
                
                # Show some feature names
                logger.info("Sample feature names:")
                for i, key in enumerate(data.files):
                    if i < 10:
                        logger.info(f"  {key}: {data[key]}")
                    else:
                        break
            
            return True
        else:
            logger.error("No feature files were created")
            return False
            
    except Exception as e:
        logger.error(f"Error testing batch extraction: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False
    
    finally:
        # Clean up test files
        if os.path.exists(test_metadata_file):
            os.remove(test_metadata_file)

def test_feature_calculators():
    """Test individual feature calculators."""
    logger = get_logger(__name__)
    logger.info("Testing individual feature calculators...")
    
    try:
        # Test imports
        from codon_features import CodonFeatureCalculator
        from amino_acid_features import AminoAcidFeatureCalculator
        from genomic_features import GenomicFeatureCalculator
        from taxonomy_features import TaxonomyFeatureCalculator
        from pathway_features import PathwayFeatureCalculator
        from heg_analysis import HEGAnalyzer
        
        logger.info("All feature calculators imported successfully")
        
        # Test basic initialization
        codon_calc = CodonFeatureCalculator()
        aa_calc = AminoAcidFeatureCalculator()
        genomic_calc = GenomicFeatureCalculator()
        taxonomy_calc = TaxonomyFeatureCalculator()
        pathway_calc = PathwayFeatureCalculator()
        heg_analyzer = HEGAnalyzer()
        
        logger.info("All feature calculators initialized successfully")
        
        # Test taxonomy calculator with a sample taxid
        tax_features = taxonomy_calc.calculate_features(267748)  # From first genome in metadata
        logger.info(f"Taxonomy calculator returned {len(tax_features)} features")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing feature calculators: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Main test function."""
    # Setup logging
    setup_logging("INFO")
    logger = get_logger(__name__)
    
    logger.info("Starting DeepMuOGT feature extraction system tests...")
    
    # Test results
    results = {}
    
    # Test 1: Feature calculator imports and initialization
    logger.info("\n" + "="*50)
    logger.info("TEST 1: Feature Calculator Initialization")
    logger.info("="*50)
    results['calculators'] = test_feature_calculators()
    
    # Test 2: Single genome feature extraction
    logger.info("\n" + "="*50)
    logger.info("TEST 2: Single Genome Feature Extraction")
    logger.info("="*50)
    results['single_genome'] = test_single_genome()
    
    # Test 3: Batch feature extraction
    logger.info("\n" + "="*50)
    logger.info("TEST 3: Batch Feature Extraction")
    logger.info("="*50)
    results['batch_extraction'] = test_batch_extraction()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("All tests passed! DeepMuOGT feature extraction system is working correctly.")
        return 0
    else:
        logger.error("Some tests failed. Please check the logs above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
