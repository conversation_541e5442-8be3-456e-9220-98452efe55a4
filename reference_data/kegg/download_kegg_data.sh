#!/bin/bash
# Download KEGG data and create pathway mapping file

# Create directory structure
mkdir -p data

# Change to the KEGG data directory
cd data

# Download KEGG data files
echo "Downloading KEGG pathway data..."
curl -o pathway.list "https://rest.kegg.jp/list/pathway"
curl -o ko_pathway.list "https://rest.kegg.jp/link/pathway/ko"
curl -o pathway_ko.list "https://rest.kegg.jp/link/ko/pathway"
curl -o module.list "https://rest.kegg.jp/list/module"
curl -o ko_module.list "https://rest.kegg.jp/link/module/ko"

# Download KO information (this contains the function descriptions)
echo "Downloading KO function information..."
curl -o ko.list "https://rest.kegg.jp/list/ko"

# Return to the main directory
cd ../

# Create the pathway mapping file
echo "Creating pathway mapping file..."
python create_pathway_mapping.py \
    --pathway-list data/pathway.list \
    --ko-pathway data/ko_pathway.list \
    --pathway-ko data/pathway_ko.list \
    --module-list data/module.list \
    --ko-module data/ko_module.list \
    --ko-list data/ko.list \
    --output ./pathway_mapping.txt

echo "Done! Pathway mapping file created at ./pathway_mapping.txt"
