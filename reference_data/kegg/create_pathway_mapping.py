#!/usr/bin/env python3
"""
Create a KEGG pathway mapping file for DeepMu from KEGG API data.

This script processes KEGG API data files to create a structured pathway mapping file
that can be used by the PathwayFeatureCalculator in DeepMu. It includes detailed
function information for each KO.

Usage:
    # First download the KEGG data files:
    curl -o pathway.list "https://rest.kegg.jp/list/pathway"
    curl -o ko_pathway.list "https://rest.kegg.jp/link/pathway/ko"
    curl -o pathway_ko.list "https://rest.kegg.jp/link/ko/pathway"
    curl -o module.list "https://rest.kegg.jp/list/module"
    curl -o ko_module.list "https://rest.kegg.jp/link/module/ko"
    curl -o ko.list "https://rest.kegg.jp/list/ko"
    
    # Then run the script:
    python create_pathway_mapping_final.py \
        --pathway-list pathway.list \
        --ko-pathway ko_pathway.list \
        --pathway-ko pathway_ko.list \
        --module-list module.list \
        --ko-module ko_module.list \
        --ko-list ko.list \
        --output pathway_mapping.txt
"""

import os
import argparse
import re
from collections import defaultdict

def parse_kegg_list(filename):
    """Parse a KEGG list file into a dictionary."""
    result = {}
    if not os.path.exists(filename):
        print(f"Warning: File {filename} not found")
        return result
        
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 2:
                key = parts[0].strip()
                value = parts[1].strip()
                result[key] = value
    return result

def parse_kegg_link(filename):
    """Parse a KEGG link file into a dictionary of lists."""
    result = defaultdict(list)
    if not os.path.exists(filename):
        print(f"Warning: File {filename} not found")
        return result
        
    with open(filename, 'r') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 2:
                source = parts[0].strip()
                target = parts[1].strip()
                result[source].append(target)
    return result

def extract_pathway_category(pathway_name):
    """Extract the category from a pathway name."""
    # Common pathway categories in KEGG
    categories = [
        "Carbohydrate metabolism",
        "Energy metabolism",
        "Lipid metabolism",
        "Nucleotide metabolism",
        "Amino acid metabolism",
        "Metabolism of other amino acids",
        "Glycan biosynthesis and metabolism",
        "Metabolism of cofactors and vitamins",
        "Metabolism of terpenoids and polyketides",
        "Biosynthesis of other secondary metabolites",
        "Xenobiotics biodegradation and metabolism",
        "Genetic Information Processing",
        "Environmental Information Processing",
        "Cellular Processes",
        "Organismal Systems",
        "Human Diseases",
        "Drug Development"
    ]
    
    # Try to match a known category
    for category in categories:
        if category.lower() in pathway_name.lower():
            return category
    
    # Try to extract category based on common patterns
    if "metabolism" in pathway_name.lower():
        return "Metabolism"
    if "biosynthesis" in pathway_name.lower():
        return "Biosynthesis"
    if "degradation" in pathway_name.lower():
        return "Degradation"
    
    # Default category
    return "Other"

def extract_ec_number(ko_description):
    """Extract EC number from KO description if present."""
    ec_match = re.search(r'\[EC:([^\]]+)\]', ko_description)
    if ec_match:
        return ec_match.group(1)
    return ""

def create_pathway_mapping(pathway_list, ko_pathway, pathway_ko, module_list, ko_module, ko_list, output_file):
    """Create a pathway mapping file from KEGG data files."""
    # Parse KEGG data files
    pathway_info = parse_kegg_list(pathway_list)
    ko_to_pathway = parse_kegg_link(ko_pathway)
    pathway_to_ko = parse_kegg_link(pathway_ko)
    module_info = parse_kegg_list(module_list)
    ko_to_module = parse_kegg_link(ko_module)
    ko_info = parse_kegg_list(ko_list)
    
    # Filter out map pathways and keep only ko pathways
    ko_pathways = {}
    for pathway_id, pathway_name in pathway_info.items():
        # Convert map00010 to ko00010 if needed
        if pathway_id.startswith('map'):
            ko_id = 'ko' + pathway_id[3:]
            if ko_id not in ko_pathways:
                ko_pathways[ko_id] = pathway_name
        elif pathway_id.startswith('ko'):
            ko_pathways[pathway_id] = pathway_name
    
    # Create pathway categories
    pathway_categories = {}
    for pathway_id, pathway_name in ko_pathways.items():
        # Extract category from pathway name
        category = extract_pathway_category(pathway_name)
        pathway_categories[pathway_id] = category
    
    # Write the mapping file
    with open(output_file, 'w') as f:
        # Write header
        f.write("# KEGG Pathway Mapping File for DeepMu\n")
        f.write("# Format: [Category] pathway_id\tpathway_name\n")
        f.write("#         K00001\tko_function [EC:number]\n\n")
        
        # Group pathways by category
        category_pathways = defaultdict(list)
        for pathway_id, pathway_name in ko_pathways.items():
            category = pathway_categories.get(pathway_id, "Other")
            category_pathways[category].append((pathway_id, pathway_name))
        
        # Count KOs with functions
        ko_with_function = 0
        ko_without_function = 0
        
        # Write pathways by category
        for category in sorted(category_pathways.keys()):
            f.write(f"[{category}]\n")
            
            for pathway_id, pathway_name in sorted(category_pathways[category]):
                f.write(f"{pathway_id}\t{pathway_name}\n")
                
                # Write KOs for this pathway
                pathway_kos = pathway_to_ko.get(f"path:{pathway_id}", [])
                if not pathway_kos:
                    # Try with map prefix
                    map_id = 'map' + pathway_id[2:]
                    pathway_kos = pathway_to_ko.get(f"path:{map_id}", [])
                
                # Process KOs
                for ko_entry in pathway_kos:
                    if ko_entry.startswith('ko:'):
                        ko_id = ko_entry.replace('ko:', '')
                        
                        # Look up the KO function - ko.list has entries without the "ko:" prefix
                        ko_description = ko_info.get(ko_id, "unknown function")
                        
                        if ko_description != "unknown function":
                            ko_with_function += 1
                        else:
                            ko_without_function += 1
                            
                        # Format the KO description
                        ec_number = extract_ec_number(ko_description)
                        if ec_number:
                            # If EC number is in the description, keep it
                            f.write(f"{ko_id}\t{ko_description}\n")
                        else:
                            # If no EC number, just use the description
                            f.write(f"{ko_id}\t{ko_description}\n")
                
                f.write("\n")
            
            f.write("\n")
    

    # Print statistics
    print(f"Processed {len(ko_pathways)} pathways in {len(category_pathways)} categories")
    print(f"Categories: {', '.join(sorted(category_pathways.keys()))}")
    print(f"KOs with function information: {ko_with_function}")
    print(f"KOs without function information: {ko_without_function}")

def main():
    parser = argparse.ArgumentParser(description='Create enhanced KEGG pathway mapping file for DeepMu')
    parser.add_argument('--pathway-list', required=True, help='Path to pathway.list file')
    parser.add_argument('--ko-pathway', required=True, help='Path to ko_pathway.list file')
    parser.add_argument('--pathway-ko', required=True, help='Path to pathway_ko.list file')
    parser.add_argument('--module-list', required=True, help='Path to module.list file')
    parser.add_argument('--ko-module', required=True, help='Path to ko_module.list file')
    parser.add_argument('--ko-list', required=True, help='Path to ko.list file')
    parser.add_argument('--output', default='pathway_mapping.txt', help='Output file path')
    args = parser.parse_args()
    
    create_pathway_mapping(
        args.pathway_list,
        args.ko_pathway,
        args.pathway_ko,
        args.module_list,
        args.ko_module,
        args.ko_list,
        args.output
    )
    print(f"Enhanced pathway mapping file created: {args.output}")

if __name__ == '__main__':
    main()
